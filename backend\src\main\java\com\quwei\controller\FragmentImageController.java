package com.quwei.controller;

import com.quwei.dto.FragmentImageResponseDTO;
import com.quwei.service.FragmentImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/fragment-images")
public class FragmentImageController {

    private final FragmentImageService fragmentImageService;

    @Autowired
    public FragmentImageController(FragmentImageService fragmentImageService) {
        this.fragmentImageService = fragmentImageService;
    }

    @GetMapping
    public ResponseEntity<FragmentImageResponseDTO> getFragmentImages(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "1") int page_size) {
        return ResponseEntity.ok(fragmentImageService.getFragmentImages(page, page_size));
    }
}