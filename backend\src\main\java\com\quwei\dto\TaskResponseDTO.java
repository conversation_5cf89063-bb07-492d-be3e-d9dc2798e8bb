package com.quwei.dto;

import java.util.List;

public class TaskResponseDTO {
    private List<TaskDTO> tasks;
    private Pagination pagination;

    public List<TaskDTO> getTasks() { return tasks; }
    public void setTasks(List<TaskDTO> tasks) { this.tasks = tasks; }
    public Pagination getPagination() { return pagination; }
    public void setPagination(Pagination pagination) { this.pagination = pagination; }

    public static class Pagination {
        private long total;
        private int page;
        private int page_size;
        private int pages;

        public Pagination(long total, int page, int page_size, int pages) {
            this.total = total;
            this.page = page;
            this.page_size = page_size;
            this.pages = pages;
        }

        public long getTotal() { return total; }
        public void setTotal(long total) { this.total = total; }
        public int getPage() { return page; }
        public void setPage(int page) { this.page = page; }
        public int getPage_size() { return page_size; }
        public void setPage_size(int page_size) { this.page_size = page_size; }
        public int getPages() { return pages; }
        public void setPages(int pages) { this.pages = pages; }
    }
}