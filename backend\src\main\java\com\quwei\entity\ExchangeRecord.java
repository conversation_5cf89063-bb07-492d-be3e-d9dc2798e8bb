package com.quwei.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "exchange_records")
public class ExchangeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "reward_id", nullable = false)
    private Integer rewardId;

    @Column(name = "exchange_time")
    private LocalDateTime exchangeTime;

    @Column(name = "exchange_date")
    private LocalDateTime exchangeDate;

    @Column(name = "fragments_used")
    private Integer fragmentsUsed;

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getRewardId() { return rewardId; }
    public void setRewardId(Integer rewardId) { this.rewardId = rewardId; }
    public LocalDateTime getExchangeTime() { return exchangeTime; }
    public void setExchangeTime(LocalDateTime exchangeTime) { this.exchangeTime = exchangeTime; }
    public LocalDateTime getExchangeDate() { return exchangeDate; }
    public void setExchangeDate(LocalDateTime exchangeDate) { this.exchangeDate = exchangeDate; }
    public Integer getFragmentsUsed() { return fragmentsUsed; }
    public void setFragmentsUsed(Integer fragmentsUsed) { this.fragmentsUsed = fragmentsUsed; }
    
}