package com.quwei.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@RestController
@RequestMapping("/api/upload")
public class ImageUploadController {
    private static final Logger logger = LoggerFactory.getLogger(ImageUploadController.class);
    // 固定图片存储路径
    private static final String IMAGE_NAME = "growth_center.jpg";

    private String getUploadDirectory() {
        return Paths.get(System.getProperty("user.dir"), "uploads", "images").toString();
    }

    @PostMapping("/growth-image")
    public ResponseEntity<String> uploadGrowthImage(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件是否为空
            if (file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("上传文件不能为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || (!contentType.equals("image/jpeg") && !contentType.equals("image/png"))) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("只支持JPG和PNG格式的图片");
            }

            // 验证文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("文件大小不能超过2MB");
            }

            // 确保上传目录存在
            String uploadDir = getUploadDirectory();
            Path uploadDirPath = Paths.get(uploadDir);
            if (!Files.exists(uploadDirPath)) {
                Files.createDirectories(uploadDirPath);
            }

            // 保存文件，覆盖原有文件
            Path filePath = uploadDirPath.resolve(IMAGE_NAME);
            Files.write(filePath, file.getBytes());

            logger.info("图片上传成功，路径: {}", filePath);
            return ResponseEntity.ok("图片上传成功");
        } catch (IOException e) {
            logger.error("图片上传失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("图片上传失败: " + e.getMessage());
        }
    }
}