import axios from 'axios';

// 配置 axios 默认值
// 直接使用相对路径，让Nginx代理处理
axios.defaults.baseURL = '';
axios.defaults.headers.common['Content-Type'] = 'application/json';

// 请求拦截器
axios.interceptors.request.use(
  config => {
    console.log('Request:', {
      method: config.method,
      url: config.url,
      data: config.data,
      headers: config.headers
    });
    return config;
  },
  error => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器处理错误
axios.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    if (error.response) {
      // 处理后端返回的错误
      console.error('API Error:', error.response.data, error.response.status, error.response.headers);
    } else if (error.request) {
      // 请求发送失败
      console.error('Request Error:', error.request);
    } else {
      // 其他错误
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default axios;
