import { createApp } from 'vue'
import './style.css'
import './config.js'  // 确保axios配置被加载
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
console.log('Element Plus Icons:', ElementPlusIconsVue)
import router from './router'

const app = createApp(App)
app.use(ElementPlus)
app.use(router)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
