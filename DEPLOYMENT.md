# 趣味儿童任务管理系统 - 腾讯云服务器部署指南

## 项目概述
- **前端**: Vue 3 + Vite + Element Plus
- **后端**: Spring Boot 3.1.2 + JPA + MySQL
- **数据库**: MySQL 8.0
- **部署方式**: 腾讯云服务器 + Docker + Nginx

## 一、腾讯云服务器准备

### 1.1 购买腾讯云服务器
1. 登录腾讯云控制台
2. 选择云服务器CVM，推荐配置：
   - **CPU**: 2核
   - **内存**: 4GB
   - **硬盘**: 40GB SSD
   - **带宽**: 5Mbps
   - **操作系统**: Ubuntu 20.04 LTS

### 1.2 配置安全组
在腾讯云控制台配置安全组规则：
```
入站规则：
- 22端口 (SSH)
- 80端口 (HTTP)
- 443端口 (HTTPS)
- 8080端口 (后端API，可选)
- 3306端口 (MySQL，仅内网访问)
```

### 1.3 连接服务器
```bash
ssh root@你的服务器IP
```

## 二、TencentOS服务器环境安装

### 2.1 更新系统
```bash
# TencentOS使用yum包管理器
yum update -y
```

### 2.2 安装必要工具
```bash
# 安装基础工具
yum install -y git vim wget curl unzip

# 安装开发工具
yum groupinstall -y "Development Tools"
```

### 2.3 安装Docker
```bash
# 安装Docker依赖
yum install -y yum-utils device-mapper-persistent-data lvm2

# 添加Docker官方仓库
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker CE
yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 验证安装
docker --version

# 将当前用户添加到docker组（可选）
usermod -aG docker $USER
```

### 2.4 安装Docker Compose（多种方式，选择最快的）

#### 方式1：使用国内镜像加速（推荐）
```bash
# 使用国内镜像源下载（速度更快）
curl -L "https://get.daocloud.io/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 如果上面的链接不可用，尝试这个
# wget -O /usr/local/bin/docker-compose "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)"

# 添加执行权限
chmod +x /usr/local/bin/docker-compose

# 创建软链接
ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose

# 验证安装
docker-compose --version
```

#### 方式2：使用pip安装（最快）
```bash
# 安装Python和pip
yum install -y python3 python3-pip

# 使用pip安装docker-compose（通常比下载二进制文件快）
pip3 install docker-compose

# 验证安装
docker-compose --version
```

#### 方式3：使用yum安装（如果可用）
```bash
# 尝试从EPEL仓库安装
yum install -y epel-release
yum install -y docker-compose

# 验证安装
docker-compose --version
```

#### 方式4：手动下载（如果网络太慢）
```bash
# 如果网络实在太慢，可以在本地下载后上传到服务器
# 本地下载地址：https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-linux-x86_64
# 然后使用scp上传：
# scp docker-compose-linux-x86_64 root@你的服务器IP:/usr/local/bin/docker-compose

# 上传后设置权限
chmod +x /usr/local/bin/docker-compose
ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose
```

### 2.5 配置防火墙
```bash
# 启动firewalld
systemctl start firewalld
systemctl enable firewalld

# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --permanent --add-service=ssh

# 重载防火墙配置
firewall-cmd --reload

# 查看开放的端口
firewall-cmd --list-all
```

## 三、项目文件上传与准备

### 3.1 上传项目文件到服务器
```bash
# 在服务器上创建项目目录
mkdir -p /opt/quwei
cd /opt/quwei

# 方法1：使用git克隆（如果项目在git仓库）
git clone https://github.com/your-username/quwei.git .

# 方法2：使用scp上传本地文件
# 在本地执行：
# scp -r ./quwei root@你的服务器IP:/opt/
```

### 3.2 创建后端Dockerfile
在服务器上创建 `backend/Dockerfile`：
```bash
cat > backend/Dockerfile << 'EOF'
FROM openjdk:17-jdk-slim

WORKDIR /app

# 复制Gradle构建文件
COPY build.gradle settings.gradle gradlew ./
COPY gradle ./gradle

# 复制源代码
COPY src ./src

# 构建应用
RUN chmod +x gradlew && ./gradlew bootJar

# 运行应用
EXPOSE 8080
CMD ["java", "-jar", "build/libs/demo-0.0.1-SNAPSHOT.jar"]
EOF
```

### 3.3 创建前端Dockerfile
```bash
cat > frontend/Dockerfile << 'EOF'
# 构建阶段
FROM node:18-alpine AS build

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 构建项目
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=build /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
EOF
```

### 3.4 创建Nginx配置文件
```bash
cat > frontend/nginx.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API反向代理
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源代理
    location /images/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
```

### 3.5 创建生产环境配置文件
```bash
cat > backend/src/main/resources/application-prod.properties << 'EOF'
spring.application.name=quwei

# 数据库连接配置
spring.datasource.url=******************************************************************************************
spring.datasource.username=root
spring.datasource.password=QuWei@2025!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# 日志配置
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN

# 服务器配置
server.port=8080

# CORS配置
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
EOF
```

### 3.6 修改后端CORS配置
```bash
# 修改WebConfig.java以支持生产环境
cat > backend/src/main/java/com/quwei/config/WebConfig.java << 'EOF'
package com.quwei.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.nio.file.Paths;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*") // 生产环境允许所有来源
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false) // 生产环境设置为false
                .maxAge(3600);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        String uploadDir = Paths.get(System.getProperty("user.dir"), "uploads", "images").toString();
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:" + uploadDir + "/");
    }
}
EOF
```

### 3.7 创建Docker Compose配置文件
```bash
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: quwei-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=QuWei@2025!
      - MYSQL_DATABASE=quwei
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    volumes:
      - mysql-data:/var/lib/mysql
      - ./quwei.sql:/docker-entrypoint-initdb.d/init.sql
    restart: always
    command: --default-authentication-plugin=mysql_native_password

  backend:
    build: ./backend
    container_name: quwei-backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=******************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=QuWei@2025!
    depends_on:
      - mysql
    restart: always
    volumes:
      - ./backend/uploads:/app/uploads

  frontend:
    build: ./frontend
    container_name: quwei-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: always

volumes:
  mysql-data:
EOF
```

## 四、部署执行步骤

### 4.1 进入项目目录
```bash
cd /opt/quwei
```

### 4.2 创建必要的目录
```bash
# 创建上传目录
mkdir -p backend/uploads/images

# 设置权限
chmod -R 755 backend/uploads
```

### 4.3 构建并启动服务
```bash
# 构建并启动所有服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4.4 验证部署
```bash
# 检查容器状态
docker ps

# 检查MySQL连接
docker exec -it quwei-mysql mysql -uroot -p'QuWei@2025!' -e "SHOW DATABASES;"

# 检查后端API
curl http://localhost:8080/api/tasks

# 检查前端访问
curl http://localhost
```

## 五、域名配置（可选）

### 5.1 配置域名解析
如果您有域名，在域名服务商处添加A记录：
```
类型: A
主机记录: @ 或 www
记录值: 您的服务器IP
TTL: 600
```

### 5.2 修改Nginx配置支持域名
```bash
# 修改nginx配置
cat > frontend/nginx.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /usr/share/nginx/html;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API反向代理
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源代理
    location /images/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF

# 重新构建前端容器
docker-compose up -d --build frontend
```

## 六、SSL证书配置（推荐）

### 6.1 安装Certbot
```bash
yum install -y certbot python3-certbot-nginx
```

### 6.2 获取SSL证书
```bash
# 停止nginx容器
docker-compose stop frontend

# 获取证书
certbot certonly --standalone -d your-domain.com -d www.your-domain.com

# 启动nginx容器
docker-compose start frontend
```

## 七、维护与监控

### 7.1 常用维护命令
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 更新服务
docker-compose pull
docker-compose up -d

# 备份数据库
docker exec quwei-mysql mysqldump -uroot -p'QuWei@2025!' quwei > backup_$(date +%Y%m%d_%H%M%S).sql

# 清理无用镜像
docker system prune -f
```

### 7.2 性能监控
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -h
```

## 八、故障排除

### 8.1 常见问题及解决方案

**问题1：容器无法启动**
```bash
# 查看详细日志
docker-compose logs [service_name]

# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8080
netstat -tlnp | grep :3306
```

**问题2：数据库连接失败**
```bash
# 检查MySQL容器状态
docker exec -it quwei-mysql mysql -uroot -p'QuWei@2025!' -e "SELECT 1"

# 检查网络连接
docker network ls
docker network inspect quwei_default
```

**问题3：前端无法访问后端API**
```bash
# 检查后端服务
curl http://localhost:8080/api/tasks

# 检查nginx配置
docker exec -it quwei-frontend nginx -t

# 重新加载nginx配置
docker exec -it quwei-frontend nginx -s reload
```

**问题4：权限问题**
```bash
# 修复文件权限
chown -R root:root /opt/quwei
chmod -R 755 /opt/quwei
chmod -R 755 /opt/quwei/backend/uploads
```

### 8.2 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs backend
docker-compose logs frontend

# 实时查看日志
docker-compose logs -f --tail=100
```

## 九、安全建议

1. **修改默认密码**：将MySQL密码改为更安全的密码
2. **定期备份**：设置定时任务备份数据库
3. **更新系统**：定期更新系统和Docker镜像
4. **监控日志**：定期检查应用日志，发现异常及时处理
5. **防火墙配置**：只开放必要的端口

## 十、部署完成检查清单

- [ ] 服务器环境安装完成
- [ ] 项目文件上传完成
- [ ] Docker和Docker Compose安装成功
- [ ] 防火墙配置正确
- [ ] 所有容器启动成功
- [ ] 数据库初始化完成
- [ ] 前端页面可以正常访问
- [ ] 后端API可以正常调用
- [ ] 域名解析配置（如有）
- [ ] SSL证书配置（如有）

部署完成后，您可以通过 `http://您的服务器IP` 访问应用！