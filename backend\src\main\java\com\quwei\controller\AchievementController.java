package com.quwei.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import com.quwei.service.AchievementService;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/achievements")
public class AchievementController {

    @Autowired
    private AchievementService achievementService;

    @GetMapping("/status")
    public Map<String, Boolean> getAchievementStatus(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        LocalDate currentDate = date != null ? date : LocalDate.now();
        
        Map<String, Boolean> status = new HashMap<>();
        status.put("completeAllDailyTasks", achievementService.checkAllDailyTasksCompleted(currentDate));
        status.put("completeAllExtraTasks", achievementService.checkAllExtraTasksCompleted(currentDate));
        status.put("unlockAllGridCells", achievementService.checkAllGridCellsUnlocked());
        status.put("totalTaskTimeOver2Hours", achievementService.checkTotalTaskTimeOver2Hours(currentDate));
        
        return status;
    }
}