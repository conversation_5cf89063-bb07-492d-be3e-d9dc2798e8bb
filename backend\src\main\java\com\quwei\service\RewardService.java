package com.quwei.service;

import com.quwei.dto.RewardResponseDTO;
import java.util.Map;
import com.quwei.entity.Reward;

public interface RewardService {
    RewardResponseDTO getRewards(int page, int pageSize, String sort);
    boolean updateReward(Integer id, String name, String description, Integer fragmentCount, Integer stock);
    boolean deleteReward(Integer id);
    boolean createReward(String name, String description, Integer fragmentCount, Integer stock);
    Reward getRewardById(Integer id);
}