package com.quwei.dto;

import java.time.LocalDateTime;
import java.util.List;

public class TaskDetailDTO {
    private Integer id;
    private String title;
    private String description;
    private String status;
    private String priority;
    private LocalDateTime createdAt;
    private LocalDateTime beginAt;
    private LocalDateTime completedAt;
    private Integer experiencePoints;
    private Integer fragmentReward;
    private List<String> answerTags;

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public String getPriority() { return priority; }
    public void setPriority(String priority) { this.priority = priority; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getBeginAt() { return beginAt; }
    public void setBeginAt(LocalDateTime beginAt) { this.beginAt = beginAt; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    public Integer getExperiencePoints() { return experiencePoints; }
    public void setExperiencePoints(Integer experiencePoints) { this.experiencePoints = experiencePoints; }
    public Integer getFragmentReward() { return fragmentReward; }
    public void setFragmentReward(Integer fragmentReward) { this.fragmentReward = fragmentReward; }
    public List<String> getAnswerTags() { return answerTags; }
    public void setAnswerTags(List<String> answerTags) { this.answerTags = answerTags; }
}