package com.quwei.service.impl;

import com.quwei.dto.FragmentImageDTO;
import com.quwei.dto.FragmentImageResponseDTO;
import com.quwei.entity.FragmentImage;
import com.quwei.repository.FragmentImageRepository;
import com.quwei.service.FragmentImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FragmentImageServiceImpl implements FragmentImageService {

    private final FragmentImageRepository fragmentImageRepository;

    @Autowired
    public FragmentImageServiceImpl(FragmentImageRepository fragmentImageRepository) {
        this.fragmentImageRepository = fragmentImageRepository;
    }

    @Override
    public FragmentImageResponseDTO getFragmentImages(int page, int pageSize) {
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        Page<FragmentImage> fragmentImagePage = fragmentImageRepository.findAll(pageable);

        List<FragmentImageDTO> fragmentImageDTOs = fragmentImagePage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        FragmentImageResponseDTO responseDTO = new FragmentImageResponseDTO();
        responseDTO.setFragmentImages(fragmentImageDTOs);
        responseDTO.setPagination(new FragmentImageResponseDTO.Pagination(
                fragmentImagePage.getTotalElements(),
                page,
                pageSize,
                fragmentImagePage.getTotalPages()
        ));

        return responseDTO;
    }

    private FragmentImageDTO convertToDTO(FragmentImage fragmentImage) {
        FragmentImageDTO dto = new FragmentImageDTO();
        dto.setId(fragmentImage.getId());
        dto.setImage_url(fragmentImage.getImageUrl());
        dto.setDescription(fragmentImage.getDescription());
        dto.setCreated_at(fragmentImage.getCreatedAt());
        return dto;
    }
}