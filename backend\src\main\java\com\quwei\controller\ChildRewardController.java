package com.quwei.controller;

import com.quwei.dto.ApiResponse;
import com.quwei.dto.ChildRewardDTO;
import com.quwei.service.ChildRewardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/child/rewards")
public class ChildRewardController {
    private static final Logger logger = LoggerFactory.getLogger(ChildRewardController.class);
    private final ChildRewardService childRewardService;

    @Autowired
    public ChildRewardController(ChildRewardService childRewardService) {
        this.childRewardService = childRewardService;
    }

    @GetMapping
    public ResponseEntity<ApiResponse<Page<ChildRewardDTO>>> getRewards(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "4") int size) {
        try {
            Page<ChildRewardDTO> rewards = childRewardService.getPaginatedRewards(page, size);
            return ResponseEntity.ok(ApiResponse.success(rewards));
        } catch (Exception e) {
            logger.error("Failed to get child rewards", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error(500, "获取奖励列表失败: " + e.getMessage()));
        }
    }
}