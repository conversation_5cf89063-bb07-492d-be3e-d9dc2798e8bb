<template>
  <header class="app-header">
    <div class="header-content">
      <h1 class="page-title">敖丙学习助手平台</h1>
      <button class="switch-button" @click="toggleView">
  {{ route.path === '/' ? '切换家长端' : '切换孩子端' }}
</button>
    </div>
  </header>
</template>

<script>
import { useRouter, useRoute } from 'vue-router';

export default {
  name: 'AppHeader',
  setup() {
    const router = useRouter();
    const route = useRoute();

    const toggleView = () => {
      if (route.path === '/') {
        router.push('/parent');
      } else {
        router.push('/');
      }
    };

    return {
      toggleView,
      route
    };
  }
};
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}
</style>

<style scoped>
.app-header {
  width: 100%;
  background-color: #ffffff;
  color: #333333;
  padding: 16px 0;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin: 0;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  width: 100%;
  padding: 0 10%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 平板适配 */
@media (max-width: 1024px) {
  .header-content {
    padding: 0 5%;
  }

  .page-title {
    font-size: 20px !important;
  }

  .switch-button {
    padding: 6px 12px !important;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 3%;
    flex-direction: column;
    gap: 10px;
  }

  .page-title {
    font-size: 18px !important;
  }
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.switch-button {
  padding: 8px 16px;
  background-color: white;
  color: #409eff;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.switch-button:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
</style>