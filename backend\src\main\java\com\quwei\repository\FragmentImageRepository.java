package com.quwei.repository;

import com.quwei.entity.FragmentImage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FragmentImageRepository extends JpaRepository<FragmentImage, Integer> {
    @Override
    Page<FragmentImage> findAll(Pageable pageable);
}