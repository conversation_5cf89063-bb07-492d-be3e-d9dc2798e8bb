<template>
  <div class="dashboard">
    <AppHeader />
      <div class="card-wrapper">
        <div class="stats-container">
        <div class="stat-card">
          <div class="stat-header">
            <div class="header-content">
              <h3>本周任务完成率</h3>
            </div>
            <span class="icon checkmark">✓</span>
          </div>
          <div class="stat-value">{{ Math.round(weeklyCompletionRate) }}%</div>
          <div class="progress-bar">
            <div class="progress" :style="{ width: Math.round(weeklyCompletionRate) + '%' }"></div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-header">
            <div class="header-content">
              <h3>当前碎片总数</h3>
            </div>
            <span class="icon diamond">💎</span>
          </div>
          <div class="stat-value">{{ totalFragments }}个</div>
          <div class="stat-details">
            <div>日常任务 <span>{{ dailyFragments }}个</span></div>
            <div>额外奖励 <span>{{ extraFragments }}个</span></div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-header">
            <div class="header-content">
              <h3>每天任务时间</h3>
            </div>
            <span class="icon rocket">🚀</span>
          </div>
          <div class="stat-value">{{ dailyTaskDuration }}</div>
          <div class="level-progress">
          </div>
                </div>
      </div>
       <div style="display: flex; gap: 20px; margin-top: 10px;">
        <!-- 左侧数据统计面板 (2/3宽度) -->
        <div style="flex: 2; background-color: white; padding: 20px; border-radius: 8px;">
          <h2 style="margin-top: 0; color: #333; font-size: 18px; font-weight: 600; text-align: left;">数据统计面板</h2>
          <!-- 近7天任务完成率折线图 -->
            <div id="completionRateChart" style="width: 100%; height: 300px; margin-top: 20px;"></div>
            <!-- 数据统计内容将在这里添加 -->
            <!-- 饼状图和详情区域 -->
            <h2 style="margin-top: 20px; color: #333; font-size: 16px; font-weight: normal; text-align: left;">碎片获取分析</h2>
            <div style="display: flex; gap: 20px; margin-top: 20px;">
              <!-- 饼状图容器 -->
              <div style="flex: 1; height: 300px; background-color: #f5f5f5; border: 1px solid #e0e0e0; border-radius: 4px; padding: 15px;">
                <h3 style="margin-top: 0; font-size: 16px; color: #333; text-align: left; font-weight: normal;">碎片来源分布</h3>
                <div id="sourcePieChart" style="width: 100%; height: 250px;"></div>
              </div>
              <!-- 详情div -->
              <div style="flex: 1; background-color: #f5f5f5; border: 1px solid #e0e0e0; border-radius: 4px; padding: 15px;">
                <h3 style="margin-top: 0; font-size: 16px; color: #333; text-align: left; font-weight: normal;">每日碎片获取情况</h3>
                <div class="source-detail" style="text-align: left;">
                  <div style="margin-bottom: 15px;">
                    <p style="margin-bottom: 5px;">日常任务: {{ dailyFragment }}</p>
                    <div id="dailyTaskBar" style="width: 100%; height: 30px;"></div>
                  </div>
                  <div>
                    <p style="margin-bottom: 5px;">额外奖励: {{ extraFragment }}</p>
                    <div id="bonusTaskBar" style="width: 100%; height: 30px;"></div>
                  </div>
                </div>
              </div>
            </div>
        </div>
        <!-- 右侧任务管理中心 (1/3宽度) -->
        <div style="flex: 1; background-color: white; padding: 20px; border-radius: 8px;">
          <!-- 任务管理中心 -->
      <div class="task-management">
        <div class="task-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2 style="font-size: 18px; margin: 0;">任务管理中心</h2>
          <button class="add-task-btn" @click="showAddTaskModal = true" style="background-color: #409EFF; color: white; border: none; border-radius: 4px; padding: 6px 12px; cursor: pointer;">+ 添加任务</button>
        </div>

        <!-- 任务添加弹窗 -->
        <div v-if="showAddTaskModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 1000;">
          <div style="background-color: white; padding: 25px; border-radius: 8px; width: 500px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
              <h3 style="margin: 0; font-size: 18px;">{{ isEditing ? '编辑任务' : '添加新任务' }}</h3>
              <button @click="resetTaskForm" style="background: none; border: none; cursor: pointer; font-size: 20px;">×</button>
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 6px; color: #606266; text-align: left;">任务标题</label>
              <input v-model="taskTitle" type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="请输入任务标题">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 6px; color: #606266; text-align: left;">任务详情</label>
              <textarea v-model="taskDescription" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; min-height: 80px;" placeholder="请输入任务详情/若选择额外任务这里输入问题"></textarea>
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 6px; color: #606266; text-align: left;">优先级</label>
              <div style="display: flex; gap: 20px;">
                <label style="display: flex; align-items: center;">
                <input v-model="taskPriority" type="radio" name="taskPriority" value="重点任务" style="margin-right: 6px;"> 重点任务
              </label>
              <label style="display: flex; align-items: center;">
                <input v-model="taskPriority" type="radio" name="taskPriority" value="基础任务" style="margin-right: 6px;"> 基础任务
              </label>
              <label style="display: flex; align-items: center;">
                <input v-model="taskPriority" type="radio" name="taskPriority" value="额外任务" style="margin-right: 6px;"> 额外任务
              </label>
              </div>
            </div>

            <!-- 答案选择标签区域 -->
            <div v-if="taskPriority === '额外任务'" style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 6px; color: #606266; text-align: left;">答案选择</label>
              <div style="margin-bottom: 8px; text-align: left;">
                <input v-model="newTag" placeholder="输入标签内容" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 8px;">
                <button @click="addTag" style="padding: 8px 16px; background-color: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">添加</button>
              </div>
              <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                <div v-for="(tag, index) in answerTags" :key="index" style="position: relative; display: inline-flex; align-items: center; background-color: #ecf5ff; padding: 6px 12px 6px 8px; border-radius: 4px; margin-right: 8px;">
                  <span>{{ tag }}</span>
                  <button @click="removeTag(index)" style="position: absolute; top: -6px; right: -6px; width: 16px; height: 16px; border-radius: 50%; background: #fff; border: 1px solid #ddd; display: flex; justify-content: center; align-items: center; padding: 0; cursor: pointer; font-size: 12px; color: #909399;">×</button>
                </div>
              </div>
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 6px; color: #606266; text-align: left;">碎片数量</label>
              <input v-model.number="fragmentCount" type="number" min="1" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="请输入大于0的数字">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 6px; color: #606266; text-align: left;">经验值</label>
              <input v-model.number="experiencePoints" type="number" min="1" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="请输入大于0的数字">
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 25px;">
              <button @click="resetTaskForm()" style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">取消</button>
              <button @click="isEditing ? handleUpdateTask() : handleAddTask()" style="padding: 8px 16px; background-color: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">{{ isEditing ? '确认更新' : '确认添加' }}</button>
            </div>
          </div>
        </div>

        <div class="today-tasks">
          <h3 style="font-size: 16px; margin-bottom: 15px;">今日任务</h3>
          
          <!-- 动态任务列表 -->
          <div v-if="taskLoading" style="text-align: center; padding: 20px;">加载中...</div>
          <div v-else-if="tasks.length === 0" style="text-align: center; padding: 20px; color: #8c8c8c;">今日暂无任务</div>
          <div v-else>
            <div v-for="task in tasks" :key="task.id" class="task-item" :style="{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '12px', borderRadius: '6px', marginBottom: '10px', ...getTaskStatusStyle(task.status) }"><div style="display: flex; align-items: center;">
                <div style="flex: 1;">
                  <div style="font-weight: bold; text-align: left;">{{ task.title }}</div>
                  <div style="font-size: 12px;text-align: left; color: #606266;">{{ task.priority === '基础任务' ? '基础任务' : task.priority === '重点任务' ? '重点任务' : '额外任务' }}</div>
                  <div style="font-size: 12px;text-align: left; color: #909399;"><div :style="{ backgroundColor: task.status === '已完成' ? '#52c41a' : task.status === '进行中' ? '#1890ff' : '#d9d9d9', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px', display: 'inline-block', marginRight: '8px' }">{{ task.status === '已完成' ? '已完成' : task.status === '进行中' ? '进行中' : '未完成' }}</div> {{ task.completed_at ? ' ' + formatDate(task.completed_at) : '' }}</div>
                </div>
              </div>
              <button @click="editTask(task)" :disabled="task.status !== '未完成'" style="margin-left: auto; margin-right: 8px; padding: 4px 8px; background-color: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;" :style="task.status !== '未完成' ? { opacity: '0.6', cursor: 'not-allowed' } : {}">编辑</button>
              <button @click="handleDeleteTask(task)" :disabled="task.status !== '未完成'" style="padding: 4px 8px; background-color: #F56C6C; color: white; border: none; border-radius: 4px; cursor: pointer;" :style="task.status !== '未完成' ? { opacity: '0.6', cursor: 'not-allowed' } : {}">删除</button>
            </div>
          </div>
          
          <!-- 分页控件 -->
          <div class="pagination" style="display: flex; justify-content: flex-end; margin-top: 15px;">
            <button :disabled="currentTaskPage <= 1 || taskLoading" @click="handleTaskPageChange(currentTaskPage - 1)" style="width: 30px; height: 30px; border: 1px solid #ddd; border-radius: 4px; background-color: white; display: flex; justify-content: center; align-items: center; cursor: pointer; margin-left: 5px; outline: none;" :style="(currentTaskPage <= 1 || taskLoading) ? { opacity: '0.6', cursor: 'not-allowed' } : {}"> < </button>
            <span style="margin: 0 10px; line-height: 30px;">第 {{ currentTaskPage }} 页 / 共 {{ Math.ceil(totalTasks / taskPageSize) }} 页</span>
            <button :disabled="currentTaskPage >= Math.ceil(totalTasks / taskPageSize) || taskLoading" @click="handleTaskPageChange(currentTaskPage + 1)" style="width: 30px; height: 30px; border: 1px solid #ddd; border-radius: 4px; background-color: white; display: flex; justify-content: center; align-items: center; cursor: pointer; margin-left: 5px; outline: none;" :style="(currentTaskPage >= Math.ceil(totalTasks / taskPageSize) || taskLoading) ? { opacity: '0.6', cursor: 'not-allowed' } : {}">></button>
          </div>
        </div>
      </div>
      
      <div style="margin: 30px 0;"></div>
        </div>
      </div>
      <div style="display: flex; gap: 20px; margin-top: 10px;">
        <!-- 左侧奖励管理站 (2/3宽度) -->
        <div style="flex: 2; background-color: white; padding: 20px; border-radius: 8px;">
          <h2 style="margin-top: 0; color: #333; font-size: 18px; font-weight: 600; text-align: left;">奖励管理站</h2>
          <!-- 奖励库区域 -->
          <div style="margin-bottom: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
              <h3 style="font-size: 16px; color: #333; margin: 0;">奖励库</h3>
              <button @click="openAddRewardModal" style="background-color: #409EFF; color: white; border: none; border-radius: 4px; padding: 6px 12px; cursor: pointer; font-size: 14px;">+ 添加奖励</button>
            </div>
            <div style="border: 1px solid #eee; border-radius: 4px; overflow: hidden;">
              <el-table 
                :data="rewards" 
                style="width: 100%" 
                v-loading="loading">
                <el-table-column label="奖励名称" prop="name">
                  <template #default="scope">
                    <div style="display: flex; align-items: center;">
                      <span style="display: inline-block; width: 20px; height: 20px; background-color: #e6f7ff; border-radius: 4px; text-align: center; line-height: 20px; margin-right: 8px;">
                        {{ getRewardIcon(scope.row.name) }}
                      </span>
                      {{ scope.row.name }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="碎片门槛" prop="fragment_count" width="120"></el-table-column>
                <el-table-column label="库存" prop="stock" width="100">
                  <template #default="scope">
                    {{ scope.row.stock }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button 
                      type="primary" 
                      size="small" 
                      @click="handleEdit(scope.row)"
                      :icon="Edit">
                      编辑
                    </el-button>
                    <el-button 
                      type="danger" 
                      size="small" 
                      @click="handleDelete(scope.row.id)"
                      :icon="Delete">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination" style="padding: 15px; display: flex; justify-content: flex-end;">
                <el-pagination
                  v-model:current-page="currentPage"
                  :page-size="pageSize"
                  :total="total"
                  layout="total, prev, pager, next"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>

            <!-- 奖励编辑对话框 -->
            <el-dialog 
              :title="rewardDialog.isEdit ? '编辑奖励' : '添加奖励'" 
              v-model="rewardDialog.visible"
              width="500px">
              <el-form 
                :model="rewardDialog.form" 
                label-width="120px"
                @submit.prevent="handleSubmit">
                <el-form-item label="奖励名称">
                  <el-input v-model="rewardDialog.form.name" placeholder="请输入奖励名称"/>
                </el-form-item>
                <el-form-item label="奖励描述">
                  <el-input 
                    v-model="rewardDialog.form.description" 
                    type="textarea" 
                    rows="3"
                    placeholder="请输入奖励描述"/>
                </el-form-item>
                <el-form-item label="所需碎片">
                  <el-input-number 
                    v-model="rewardDialog.form.fragment_count" 
                    :min="1"
                    controls-position="right"/>
                </el-form-item>
                <el-form-item label="库存数量">
                  <el-input-number 
                    v-model="rewardDialog.form.stock" 
                    :min="0"
                    controls-position="right"/>
                  <div class="tip" style="color: #909399; font-size: 12px; margin-top: 4px;">
                    设置为0表示无限制
                  </div>
                </el-form-item>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="rewardDialog.visible = false">取消</el-button>
                  <el-button type="primary" @click="handleSubmit">
                    {{ rewardDialog.isEdit ? '更新' : '添加' }}
                  </el-button>
                </div>
              </template>
            </el-dialog>
          </div>

          <!-- 兑换记录区域 -->
          <div>
            <h3 style="font-size: 16px; color: #333; margin: 0 0 15px 0; text-align: left;">兑换记录</h3>
            <div style="border: 1px solid #eee; border-radius: 4px; overflow: hidden;">
              <table style="width: 100%; border-collapse: collapse;">
                <thead style="background-color: #f5f5f5;">
                  <tr>
                    <th style="padding: 10px; text-align: left; font-weight: normal; border-bottom: 1px solid #eee;">兑换奖励</th>
                    <th style="padding: 10px; text-align: left; font-weight: normal; border-bottom: 1px solid #eee;">消耗碎片</th>
                    <th style="padding: 10px; text-align: left; font-weight: normal; border-bottom: 1px solid #eee;">兑换日期</th>
                    <th style="padding: 10px; text-align: left; font-weight: normal; border-bottom: 1px solid #eee;">备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="record in exchangeRecords" :key="record.id" style="hover: background-color: #f9f9f9;">
                    <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;">{{ record.rewardName }}</td>
                    <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;">{{ record.fragmentsUsed }}</td>
                    <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;">{{ formatDate(record.exchange_time) }}</td>
                    <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;">查看详情</td>
                  </tr>
                  <tr v-if="exchangeRecords.length === 0 && !exchangeLoading">
                    <td colspan="4" style="text-align: center; padding: 20px; color: #909399;">暂无兑换记录</td>
                  </tr>
                </tbody>
            </table>
            <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
              <el-pagination
                v-model:current-page="exchangeCurrentPage"
                v-model:page-size="exchangePageSize"
                :total="exchangeTotal"
                @current-change="fetchExchangeRecords"
                layout="total, prev, pager, next, jumper"
              />
            </div>
          </div>
          </div>
        </div>
        <!-- 右侧系统设置 (1/3宽度) -->
        <div style="flex: 1; background-color: white; padding: 20px; border-radius: 8px;">
          <h2 style="margin-top: 0; color: #333; font-size: 18px; font-weight: 600; text-align: left;">系统设置</h2>
          <!-- 标题碎片图像上传 -->
<div class="upload-section" style="margin-bottom: 20px; padding: 20px; border: 2px dashed #ddd; border-radius: 8px; transition: all 0.3s ease;">
  <h3 style="margin-top: 0; color: #333; font-size: 16px; margin-bottom: 15px;">成长中心九宫格图片设置</h3>
  <div class="upload-area" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 30px; cursor: pointer; border-radius: 6px; transition: background-color 0.2s;" @click="$refs.fileInput.click()">
    <div style="font-size: 40px; color: #409EFF; margin-bottom: 15px;">📁</div>
    <p style="margin: 0 0 10px 0; color: #606266;">点击或拖拽图片至此处上传</p>
    <p style="margin: 0; color: #909399; font-size: 12px;">支持 JPG、PNG 格式，最大 2MB</p>
    <input type="file" accept=".jpg,.jpeg,.png" @change="handleImageUpload" style="display: none;" ref="fileInput">
  </div>
  <div v-if="imagePreviewUrl" style="margin-top: 15px; text-align: center;">
    <img :src="imagePreviewUrl" style="max-width: 200px; max-height: 150px; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
    <button @click="removeImagePreview" style="margin-top: 8px; color: #f56c6c; background: none; border: none; cursor: pointer; font-size: 12px;">移除预览</button>
  </div>
  <div v-if="imageUploadStatus" style="margin-top: 15px; padding: 8px 12px; border-radius: 4px; font-size: 14px;" :style="imageUploadStatus.includes('成功') ? {background: '#f0f9eb', color: '#52c41a'} : {background: '#fef0f0', color: '#f5222d'}">
    {{ imageUploadStatus }}
  </div>
</div>

        <!-- 上传失败弹窗 -->
        <div v-if="showUploadErrorModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: flex; justify-content: center; align-items: center; z-index: 2000;">
          <div style="background-color: white; padding: 25px; border-radius: 8px; width: 350px; text-align: center;">
            <div style="color: #f5222d; font-size: 24px; margin-bottom: 15px;">⚠️</div>
            <h3 style="margin: 0 0 15px 0; color: #333; font-size: 16px;">上传失败</h3>
            <p style="color: #606266; margin-bottom: 20px;">{{ uploadErrorMsg }}</p>
            <button @click="showUploadErrorModal = false" style="background-color: #f5222d; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer;">确定</button>
          </div>
        </div>
        </div>
      </div>
    </div>
      </div>

      <!-- 删除确认模态框 -->
      <div v-if="showDeleteModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: flex; justify-content: center; align-items: center; z-index: 1000;">
        <div style="background-color: white; padding: 20px; border-radius: 8px; width: 300px;">
          <h3 style="margin-top: 0; color: #333; font-size: 16px;">确认删除</h3>
          <p style="color: #606266; margin: 15px 0;">确定要删除任务「{{ currentDeleteTask ? currentDeleteTask.title : '' }}」吗？</p>
          <div style="display: flex; justify-content: flex-end; gap: 10px;">
            <button @click="handleDeleteCancel" style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">取消</button>
            <button @click="handleDeleteConfirm" style="padding: 8px 16px; background-color: #f56c6c; color: white; border: none; border-radius: 4px; cursor: pointer;">确认删除</button>
          </div>
        </div>
      </div>
    <!-- 新增奖励模态框 -->
<div v-if="showAddRewardModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: flex; justify-content: center; align-items: center; z-index: 1000;">
  <div style="background-color: white; padding: 20px; border-radius: 8px; width: 400px;">
    <h3 style="margin-top: 0; color: #333; font-size: 16px;">{{ isEditing ? '修改奖励' : '新增奖励' }}</h3>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px; color: #606266; text-align: left;">奖励名称</label>
      <input v-model="currentReward.name" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="请输入奖励名称">
    </div>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px; color: #606266; text-align: left;">碎片数量</label>
      <input v-model.number="currentReward.fragmentCount" type="number" min="1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="请输入大于0的数字">
    </div>
    <div style="margin-bottom: 20px;">
      <label style="display: block; margin-bottom: 5px; color: #606266; text-align: left;">库存</label>
      <input v-model.number="currentReward.stock" type="number" min="0" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="请输入大于等于0的数字">
    </div>
    <div style="display: flex; justify-content: flex-end; gap: 10px;">
      <button @click="resetTaskForm" style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">取消</button>
      <button @click="addReward" style="padding: 8px 16px; background-color: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">确定</button>
    </div>
  </div>
</div>
</template>

<script>
import AppHeader from '../components/AppHeader.vue';
import * as echarts from 'echarts';
import axios from '../config';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Edit, Delete } from '@element-plus/icons-vue';

export default {
  components: {
    AppHeader,
    Edit,
    Delete
  },
  data() {
      return {
        // 图表实例
        pieChartInstance: null,
        dailyBarChart: null,
        bonusBarChart: null,
        // 任务管理相关数据
    weeklyCompletionRate: null,
    tasks: [],

      totalTasks: 0,
      currentTaskPage: 1,
      taskPageSize: 6,
      taskLoading: false,
      // 奖励管理相关数据
      rewards: [],
      loading: false,
      currentPage: 1,
      pageSize: 4,
      total: 0,
      // 兑换记录分页相关数据
      exchangeRecords: [],
      exchangeCurrentPage: 1,
      exchangePageSize: 2,
      exchangeTotal: 0,
      exchangeLoading: false,
      // 碎片统计数据
      totalFragments: 0,
      dailyFragments: 0,
      extraFragments: 0,
      dailyFragment: 0,
      extraFragment: 0,
      rewardDialog: {
        visible: false,
        isEdit: false,
        form: {
          id: null,
          name: '',
          description: '',
          fragment_count: 1,
          stock: 0
        }
      },

      // 图像上传相关数据
      imageUploadStatus: '',
      imagePreviewUrl: '',
      showUploadErrorModal: false,
      uploadErrorMsg: '',
      showAddTaskModal: false,
      showDeleteModal: false,
      currentDeleteTask: null,
      taskTitle: '',
      taskDescription: '',
      taskPriority: '基础任务',
      experiencePoints: 10,
      answerTags: [],
      newTag: '',
      isEditing: false,
      currentTask: null,
      dailyTaskDuration: "00:00"
    }
  },
  methods: {
    async getDailyTaskDuration() {
      try {
        const response = await axios.get('/api/tasks/daily-task-duration', {
          params: { date: new Date().toISOString().split('T')[0] }
        });
        this.dailyTaskDuration = response.data;
      } catch (error) {
        console.error('获取每日任务时长失败:', error);
        this.dailyTaskDuration = '获取失败';
      }
    },
    async handleDeleteTask(task) {
      try {
        await ElMessageBox.confirm('确定要删除这个任务吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        await axios.delete(`/api/tasks/${task.id}`);
        this.fetchTasks();
        ElMessage.success('任务已删除');
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message || '网络错误'));
        }
      }
    },
    // 获取任务列表
    // 获取兑换记录列表
    async fetchExchangeRecords() {
      this.exchangeLoading = true;
      try {
        const response = await axios.get('/api/exchange-records', {
          params: {
            page: this.exchangeCurrentPage,
            page_size: this.exchangePageSize
          }
        });
        this.exchangeRecords = response.data.exchangeRecords || [];
        this.exchangeTotal = response.data.pagination?.total || 0;
      } catch (error) {
        ElMessage.error('获取兑换记录失败: ' + (error.response?.data?.message || error.message));
        this.exchangeRecords = [];
        this.exchangeTotal = 0;
      } finally {
        this.exchangeLoading = false;
      }
    },

    // 处理兑换记录分页变更
    handleExchangePageChange(page) {
      this.exchangeCurrentPage = page;
      this.fetchExchangeRecords();
    },

    async fetchWeeklyCompletionRate() {
      try {
        const response = await axios.get('/api/tasks/weekly-completion-rate');
        this.weeklyCompletionRate = response.data;
      } catch (error) {
        console.error('获取本周任务完成率失败:', error);
        this.weeklyCompletionRate = 0;
      }
    },
    async fetchWeeklyCompletionRates() {
      try {
        const response = await axios.get('/api/tasks/weekly-completion-rates');
        this.weeklyCompletionRates = response.data;
        this.initCompletionRateChart();
      } catch (error) {
        console.error('获取周完成率数据失败:', error);
      }
    },
    initCompletionRateChart() {
      const chartDom = document.getElementById('completionRateChart');
      const myChart = echarts.init(chartDom);
      
      const dates = this.weeklyCompletionRates.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}-${date.getDate()}`;
      });
      const rates = this.weeklyCompletionRates.map(item => item.completionRate);
      
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}%'
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [{
          data: rates,
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF'
          },
          lineStyle: {
            width: 3
          }
        }]
      };
      
      option && myChart.setOption(option);
    },
    async fetchFragmentStats() {
      try {
        const response = await axios.get('/api/tasks/fragment-stats');
        this.totalFragments = response.data.totalFragments || 0;
        this.dailyFragments = response.data.dailyFragments || 0;
        this.extraFragments = response.data.extraFragments || 0;
        // 更新饼图数据
        if (this.pieChartInstance) {
          this.pieChartInstance.setOption({
            series: [{
              data: [
                { value: this.dailyFragments, name: '日常任务' },
                { value: this.extraFragments, name: '额外奖励' }
              ]
            }]
          });
        }
      } catch (error) {
        console.error('获取碎片统计数据失败:', error);
      }
    },
    async fetchFragmentStat() {
      try {
        const response = await axios.get('/api/tasks/today-fragment-stats');
        this.dailyFragment = response.data.dailyTaskFragment || 0;
        this.extraFragment = response.data.extraRewardFragment || 0;

        // 更新日常任务碎片图表数据
        if (this.dailyBarChart) {
          this.dailyBarChart.setOption({
            series: [{
              type: 'bar',
              data: [this.dailyFragment]
            }]
          });
        }

        // 更新额外奖励碎片图表数据
        if (this.bonusBarChart) {
          this.bonusBarChart.setOption({
            series: [{
              type: 'bar',
              data: [this.extraFragment]
            }]
          });
        }
      } catch (error) {
        console.error('获取碎片统计数据失败:', error);
      }
    },
    fetchTasks() {
      this.taskLoading = true;
      axios.get('/api/tasks', {
        params: {
          page: this.currentTaskPage,
          page_size: this.taskPageSize
        }
      }).then(response => {
        this.tasks = response.data.tasks || [];
        this.totalTasks = response.data.pagination?.total || 0;
        this.taskLoading = false;
      }).catch(error => {
        ElMessage.error('获取任务列表失败: ' + (error.response?.data?.message || error.message));
        this.taskLoading = false;
        this.tasks = [];
      });
    },
    // 处理页码变更
    handleTaskPageChange(page) {
      this.currentTaskPage = page;
      this.fetchTasks();
    },
    // 获取任务状态样式
    getTaskStatusStyle(status) {
      switch(status) {
        case 'COMPLETED': return { backgroundColor: '#f0f9eb', borderColor: '#c2e7b0', color: '#52c41a' };
        case 'IN_PROGRESS': return { backgroundColor: '#fffbeb', borderColor: '#ffe58f', color: '#faad14' };
        default: return { backgroundColor: '#f5f5f5', borderColor: '#d9d9d9', color: '#8c8c8c' };
      }
    },
    // 格式化日期
    formatDate(dateString) {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '无效日期';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    // 添加任务处理方法
    editTask(task) {
      this.isEditing = true;
      this.currentTask = task;
      this.taskTitle = task.title;
      this.taskDescription = task.description || '';
      const priorityMap = {
        '重点任务': '重点任务',
        '基础任务': '基础任务',
        '额外任务': '额外任务'
      };
      this.taskPriority = priorityMap[task.priority] || '基础任务';
      this.experiencePoints = task.experience_points || 10;
      this.fragmentCount = task.fragment_reward || 1;
      this.isEditing = true;
      
      // 仅额外任务需要获取答案标签
      if (task.priority === '额外任务') {
        // 根据任务ID获取答案标签
        axios.get(`/api/tasks/${task.id}/answer-tags`)
          .then(response => {
            this.answerTags = response.data.data || [];
            this.showAddTaskModal = true;
          })
          .catch(error => {
            ElMessage.error('获取答案标签失败，请重试');
            console.error(error);
            this.showAddTaskModal = true; // 即使失败也显示模态框
          });
      } else {
        // 非额外任务直接显示编辑模态框
        this.showAddTaskModal = true;
      }
    },
    handleUpdateTask() {
      if (!this.currentTask || !this.currentTask.id) {
        ElMessage.error('任务信息不完整，无法更新');
        return;
      }
      if (!this.taskTitle) {
        ElMessage.error('请输入任务标题');
        return;
      }

      const taskData = {
        title: this.taskTitle,
        description: this.taskDescription,
        priority: this.taskPriority,
        experience_points: this.experiencePoints,
        fragment_reward: this.fragmentCount,
        answer_tags: this.taskPriority === '额外任务' ? this.answerTags : []
      };

      axios.put(`/api/tasks/${this.currentTask.id}`, taskData, {
        headers: {'Content-Type': 'application/json'}
      }).then(() => {
        ElMessage.success('任务更新成功');
        this.fetchTasks();
        this.resetTaskForm();
      }).catch(error => {
        ElMessage.error('任务更新失败: ' + (error.response?.data?.message || error.message));
      });
    },
    resetTaskForm() {
      this.taskTitle = '';
      this.taskDescription = '';
      this.taskPriority = '基础任务';
      this.experiencePoints = 10;
      this.fragmentCount = 1;
      this.answerTags = [];
      this.newTag = '';
      this.showAddTaskModal = false;
      this.isEditing = false;
      this.currentTask = null;
    },
    openAddTaskModal() {
      this.resetTaskForm();
      this.showAddTaskModal = true;
    },
    handleAddTask() {
      // 表单验证
      if (!this.taskTitle || !this.fragmentCount || !this.experiencePoints) {
        ElMessage.error('请填写必要的任务信息');
        return;
      }

      // 准备任务数据
      const taskData = {
        title: this.taskTitle,
        priority: this.taskPriority,
        fragmentCount: this.fragmentCount,
        experiencePoints: this.experiencePoints,
        type: this.taskPriority === '重点任务' ? 'IMPORTANT' : this.taskPriority === '基础任务' ? 'BASIC' : 'EXTRA',
        status: 'PENDING',
        answerTags: this.taskPriority === '额外任务' ? this.answerTags : []
      };

      // 发送添加任务请求
      this.taskLoading = true;
      axios.post('/api/tasks', taskData)
        .then(response => {
          ElMessage.success('任务添加成功');
          this.showAddTaskModal = false;
          this.fetchTasks(); // 刷新任务列表
          // 重置表单
          this.taskTitle = '';
          this.taskPriority = '基础任务';
          this.fragmentCount = 0;
          this.experiencePoints = 0;
          this.answerTags = [];
          this.newTag = '';
        })
        .catch(error => {
          ElMessage.error('添加任务失败: ' + (error.response?.data?.message || error.message));
        })
        .finally(() => {
          this.taskLoading = false;
        });
    },
    // 奖励管理相关方法
    async fetchRewards() {
      this.loading = true;
      try {
        const response = await axios.get(`/api/rewards?page=${this.currentPage}&size=${this.pageSize}`);
        console.log('Rewards response:', response);
        const responseData = response.data;
        if (responseData.data && responseData.data.rewards) {
            this.rewards = responseData.data.rewards;
            this.total = responseData.data.pagination?.total || 0;
        } else {
          ElMessage.error('获取奖励列表失败：数据格式不正确 - 缺少data或rewards属性');
          console.error('Unexpected response format:', responseData);
        }
      } catch (error) {
        ElMessage.error('获取奖励列表失败');
        console.error('Error fetching rewards:', error);
      } finally {
        this.loading = false;
      }
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchRewards();
    },

    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchRewards();
    },

    async handleEdit(reward) {
      this.rewardDialog.form = { ...reward };
      this.rewardDialog.isEdit = true;
      this.rewardDialog.visible = true;
    },

    async handleDelete(id) {
      try {
        await ElMessageBox.confirm('确定要删除这个奖励吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        await axios.delete(`/api/rewards/${id}`);
        ElMessage.success('删除成功');
        this.fetchRewards();
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败');
          console.error('Error deleting reward:', error);
        }
      }
    },

    async handleSubmit() {
      if (!this.rewardDialog.form.name.trim()) {
        ElMessage.warning('请输入奖励名称');
        return;
      }

      // 验证所需碎片数量
      if (!this.rewardDialog.form.fragment_count || this.rewardDialog.form.fragment_count <= 0) {
        ElMessage.warning('所需碎片数量必须大于0');
        return;
      }

      // 验证库存数量
      if (typeof this.rewardDialog.form.stock !== 'number' || this.rewardDialog.form.stock < 0) {
        ElMessage.warning('库存数量必须大于等于0');
        return;
      }

      try {
        if (this.rewardDialog.isEdit) {
          const formData = new URLSearchParams();
          formData.append('name', this.rewardDialog.form.name);
          formData.append('description', this.rewardDialog.form.description || '');
          formData.append('fragment_count', this.rewardDialog.form.fragment_count || 1);
          formData.append('stock', this.rewardDialog.form.stock || 0);
          await axios.put(`/api/rewards/${this.rewardDialog.form.id}?${formData.toString()}`);
          ElMessage.success('更新成功');
        } else {
          await axios.post('/api/rewards', {
            name: this.rewardDialog.form.name.trim(),
            description: this.rewardDialog.form.description?.trim() || '',
            fragment_count: parseInt(this.rewardDialog.form.fragment_count) || 1,
            stock: parseInt(this.rewardDialog.form.stock) || 0
          }, {
            headers: {
              'Content-Type': 'application/json'
            }
          });
          ElMessage.success('添加成功');
        }
        this.rewardDialog.visible = false;
        this.fetchRewards();
      } catch (error) {
        ElMessage.error(this.rewardDialog.isEdit ? '更新失败' : '添加失败');
        console.error('Error submitting reward:', error);
      }
    },

    openAddRewardModal() {
      this.rewardDialog.form = {
        id: null,
        name: '',
        description: '',
        fragment_count: 1,  // 确保默认值大于0
        stock: 0
      };
      this.rewardDialog.isEdit = false;
      this.rewardDialog.visible = true;
    },

    getRewardIcon(name) {
      const icons = {
        '选绘本资格': '📚',
        '游戏时间': '🎮',
        '小玩偶': '🧸',
        '绘画套装': '🎨'
      };
      return icons[name] || '🎁';
    },

    // 图片上传相关方法
    // 图片上传相关方法
    handleImageUpload(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 验证文件类型
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!validTypes.includes(file.type)) {
        this.imageUploadStatus = '❌ 不支持的文件类型，请上传JPG或PNG格式';
        this.imagePreviewUrl = '';
        return;
      }

      // 验证文件大小
      if (file.size > 2 * 1024 * 1024) {
        this.imageUploadStatus = '❌ 图片大小不能超过2MB';
        this.imagePreviewUrl = '';
        return;
      }

      // 显示图片预览
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imagePreviewUrl = e.target.result;
        this.imageUploadStatus = '⏳ 准备上传...';

        // 创建FormData并上传图片
        const formData = new FormData();
        formData.append('file', file);

        axios.post('/api/upload/growth-image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then(response => {
          this.imageUploadStatus = '✅ 图片上传成功';
          // 重置文件输入
          this.$refs.fileInput.value = '';
          // 3秒后清除状态
          setTimeout(() => this.imageUploadStatus = '', 3000);
        })
        .catch(error => {
          this.imageUploadStatus = '❌ 上传失败: ' + (error.response?.data?.message || '网络错误');
          console.error('图片上传失败:', error);
        });
      };
      reader.readAsDataURL(file);
    },
    // 移除图片预览
    removeImagePreview() {
      this.imagePreviewUrl = '';
      this.imageUploadStatus = '';
      this.$refs.fileInput.value = '';
    },
          addTag() {
            if (this.newTag.trim() && !this.answerTags.includes(this.newTag.trim())) {
              this.answerTags.push(this.newTag.trim());
              this.newTag = '';
            }
          },
          removeTag(index) {
            this.answerTags.splice(index, 1);
          },
          confirmDelete(task) {
        this.currentDeleteTask = task;
        this.showDeleteModal = true;
      },
      confirmDeleteReward(rewardName) {
        this.currentDeleteTask = { title: rewardName };
        this.showDeleteModal = true;
      },
          handleDeleteConfirm() {
            this.deleteTask(this.currentDeleteTask);
            this.showDeleteModal = false;
          },
          handleDeleteCancel() {
            this.showDeleteModal = false;
          },
          deleteTask(task) {
            // 这里添加删除任务的逻辑
            this.tasks = this.tasks.filter(t => t.id !== task.id);
          },
          async handleAddTask() {
            if (!this.taskTitle) {
              alert('请输入任务标题');
              return;
            }
            if (this.fragmentCount <= 0 || this.experiencePoints <= 0) {
              alert('碎片数量和经验值必须大于0');
              return;
            }
            // 额外任务需要至少一个标签
            if (this.taskPriority === '额外任务' && this.answerTags.length === 0) {
              alert('请为额外任务添加至少一个答案标签');
              return;
            }

            // 检查是否已存在额外任务
            if (this.taskPriority === '额外任务') {
              const existingExtraTask = this.tasks.some(task => task.priority === '额外任务');
              if (existingExtraTask) {
                ElMessage.error('额外任务只能添加一条，请先完成现有额外任务');
                return;
              }
            }

            // 这里可以添加任务提交逻辑
              try {
              await axios.post('/api/tasks', {
              title: this.taskTitle,
              description: this.taskDescription,
              priority: this.taskPriority,
              experience_points: this.experiencePoints,
              fragment_reward: this.fragmentCount,
              answer_tags: this.taskPriority === '额外任务' ? this.answerTags : []
            }, {
              headers: {
                'Content-Type': 'application/json'
              }
            });
            ElMessage.success('任务添加成功');
            this.fetchTasks(); // 添加成功后刷新任务列表
            // 重置表单并关闭弹窗
            this.taskTitle = '';
            this.taskDescription = '';
            this.taskPriority = '基础任务';
            this.fragmentCount = 1;
            this.experiencePoints = 1;
            this.resetTaskForm();
          } catch (error) {
              ElMessage.error('任务添加失败: ' + (error.response?.data?.message || error.message));
              console.error('Error submitting task:', error);
            }
          }
  },
  
  name: 'ParentDashboard',
  components: {
    AppHeader
  },
  created() {
    this.fetchRewards();
    this.fetchTasks();
    this.getDailyTaskDuration();
  },
  mounted() {
      this.fetchExchangeRecords();
      this.fetchWeeklyCompletionRate();
      this.fetchWeeklyCompletionRates();
      this.fetchFragmentStats();
      this.fetchFragmentStat();

      // 初始化任务完成率折线图
      const chartDom = document.getElementById('completionRateChart');
    const myChart = echarts.init(chartDom);

    // 近7天日期数据
    const dates = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(`${date.getMonth() + 1}/${date.getDate()}`);
    }

    // 配置图表
    const option = {
      title: {
        text: '近7天任务完成率',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 16
        },
        left: 'left'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [{
        data: [65, 78, 52, 91, 76, 85, 90], // 模拟任务完成率数据
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        },
        lineStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0)' }
          ])
        }
      }]
    };

    myChart.setOption(option);

    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      myChart.resize();
      myPieChart.resize();
    });

   

  // 初始化碎片来源饼状图
    const pieChartDom = document.getElementById('sourcePieChart');
    this.pieChartInstance = echarts.init(pieChartDom);
    const myPieChart = this.pieChartInstance;

    const pieOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 0,
        left: 'center',
        textStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '碎片来源',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: this.dailyFragments, name: '日常任务' },
            { value: this.extraFragments, name: '额外奖励' }
          ]
        }
      ]
    };

    myPieChart.setOption(pieOption);

    // 初始化日常任务柱状图
    const dailyBarDom = document.getElementById('dailyTaskBar');
    this.dailyBarChart = echarts.init(dailyBarDom);
    const dailyBarOption = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false }
      },
      yAxis: {
        type: 'category',
        data: [''],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false }
      },
      series: [{
        data: [this.dailyFragment],
        type: 'bar',
        itemStyle: {
          color: '#409EFF'
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          textStyle: {
            fontSize: 12
          }
        },
        barWidth: 20
      }]
    };
    this.dailyBarChart.setOption(dailyBarOption);

    // 初始化额外奖励柱状图
    const bonusBarDom = document.getElementById('bonusTaskBar');
    this.bonusBarChart = echarts.init(bonusBarDom);
    const bonusBarOption = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false }
      },
      yAxis: {
        type: 'category',
        data: [''],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false }
      },
      series: [{
        data: [this.extraFragment],
        type: 'bar',
        itemStyle: {
          color: '#67C23A'
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%',
          textStyle: {
            fontSize: 12
          }
        },
        barWidth: 20
      }]
    };
    this.bonusBarChart.setOption(bonusBarOption);

    // 更新窗口大小调整事件
    window.addEventListener('resize', () => {
      myChart.resize();
      myPieChart.resize();
      dailyBarChart.resize();
      bonusBarChart.resize();
    });
  }
}
</script> 

<style scoped>
.dashboard {
  width: 100%;
  margin: 0 auto;
  min-height: 100vh;
}
.content {
  margin-top: 100px;
  padding: 20px;
  min-height: calc(100vh - 100px);
}

.card-wrapper {
   width: 100%;
   /* padding: 25px; */
   box-sizing: border-box;
   /* background-color: #ffffff; */
   border-radius: 12px;
   box-shadow: 0 2px 8px rgba(0,0,0,0.1);
 }

.stats-container {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
  flex: 1;
  min-width: 300px;
  box-sizing: border-box;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.stat-header .headet {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-header h3 {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.trend-up {
  font-size: 14px;
  color: #4cd964;
  display: flex;
  align-items: center;
}

.trend-info {
  font-size: 14px;
  color: #8e8e93;
  display: flex;
  align-items: center;
}

.stat-details {
  font-size: 14px;
  color: #606266;
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.stat-details span {
  font-weight: 500;
  color: #303133;
}

.level-progress {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  font-size: 14px;
  color: #606266;
}

.progress-bar.purple {
  background: #f5f0ff;
}

.progress-bar.purple .progress {
  background: #9c27b0;
}

.icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.icon.checkmark {
  background-color: #e8f5e9;
  color: #4caf50;
}

.icon.diamond {
  background-color: #e3f2fd;
  color: #1976d2;
}

.icon.rocket {
  background-color: #f3e5f5;
  color: #8e24aa;
}

.stat-value {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
}

.progress-bar {
  height: 8px;
  background: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #409eff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 平板和移动端响应式布局 */
@media (max-width: 1024px) {
  .dashboard {
    padding: 0 10px;
  }

  .card-wrapper {
    padding: 15px;
  }

  .stats-container {
    flex-direction: column;
    gap: 15px;
  }

  .stat-card {
    min-width: auto;
    width: 100%;
  }

  .content-row {
    flex-direction: column;
    gap: 15px;
  }

  .chart-container {
    height: 300px;
  }

  .task-table {
    overflow-x: auto;
  }

  .reward-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0 5px;
  }

  .card-wrapper {
    padding: 10px;
  }

  .stat-card {
    padding: 15px;
  }

  .chart-container {
    height: 250px;
  }

  .reward-grid {
    grid-template-columns: 1fr;
  }

  .task-table th,
  .task-table td {
    padding: 8px 4px;
    font-size: 12px;
  }
}
</style>


