package com.quwei.dto;

public class ChildRewardDTO {
    private Integer id;
    private String name;
    private Integer fragmentCount;
    private boolean canRedeem;

    // 添加无参构造函数
    public ChildRewardDTO() {
    }

    public ChildRewardDTO(Integer id, String name, Integer fragmentCount, boolean canRedeem) {
        this.id = id;
        this.name = name;
        this.fragmentCount = fragmentCount;
        this.canRedeem = canRedeem;
    }

    // Getters
    public Integer getId() { return id; }
    public String getName() { return name; }
    public Integer getFragmentCount() { return fragmentCount; }
    public boolean isCanRedeem() { return canRedeem; }

    // Setters
    public void setId(Integer id) { this.id = id; }
    public void setName(String name) { this.name = name; }
    public void setFragmentCount(Integer fragmentCount) { this.fragmentCount = fragmentCount; }
    public void setCanRedeem(boolean canRedeem) { this.canRedeem = canRedeem; }
}