package com.quwei.service.impl;

import com.quwei.dto.TaskDTO;
import com.quwei.dto.TaskResponseDTO;
import com.quwei.entity.Task;
import com.quwei.repository.TaskRepository;
import com.quwei.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.quwei.dto.CreateTaskRequest;
import com.quwei.dto.UpdateTaskRequest;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.time.DayOfWeek;
import java.util.List;
import com.quwei.entity.Task.TaskPriority;
import com.quwei.dto.TaskDetailDTO;
import java.util.Map;
import java.util.stream.Collectors;
import com.quwei.dto.TodayFragmentStatsDTO;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import com.quwei.entity.Answer;
import com.quwei.repository.AnswerRepository;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final AnswerRepository answerRepository;

    @Autowired
    public TaskServiceImpl(TaskRepository taskRepository, AnswerRepository answerRepository) {
        this.taskRepository = taskRepository;
        this.answerRepository = answerRepository;
    }

    @Override
    public Integer getDailyFragments(LocalDate date) {
        return taskRepository.sumDailyFragmentsByDate(date) != null ? taskRepository.sumDailyFragmentsByDate(date) : 0;
    }

    @Override
    public List<String> getAnswerTags(Integer taskId) {
        return answerRepository.findByTaskId(taskId).stream()
            .map(Answer::getTagName)
            .collect(Collectors.toList());
    }

    @Override
    public Integer getTotalFragments() {
        return taskRepository.sumAllFragments() != null ? taskRepository.sumAllFragments() : 0;
    }

    @Override
    public Integer getTotalExperience() {
        return taskRepository.sumTotalExperience() != null ? taskRepository.sumTotalExperience() : 0;
    }

    @Override
    public Integer getDailyFragments() {
        return taskRepository.sumDailyFragments() != null ? taskRepository.sumDailyFragments() : 0;
    }

    @Override
    public Integer getExtraFragments() {
        return taskRepository.sumExtraFragments() != null ? taskRepository.sumExtraFragments() : 0;
    }

    @Override
    public TodayFragmentStatsDTO getTodayFragmentStats() {
        LocalDate today = LocalDate.now();
        Integer dailyTaskFragments = taskRepository.sumDailyFragmentsByDate(today) != null ? taskRepository.sumDailyFragmentsByDate(today) : 0;
        Integer extraRewardFragments = taskRepository.sumExtraFragmentsByDate(today) != null ? taskRepository.sumExtraFragmentsByDate(today) : 0;
        
        TodayFragmentStatsDTO stats = new TodayFragmentStatsDTO(dailyTaskFragments, extraRewardFragments);
        return stats;
    }

    @Override
    public Integer getDailyTaskDuration(LocalDate date) {
        return taskRepository.sumDailyTaskDuration(date) != null ? taskRepository.sumDailyTaskDuration(date) : 0;
    }

    @Override
    public long countDailyTasksByDate(LocalDate date) {
        return taskRepository.countDailyTasksByDate(date);
    }

    @Override
    public long countCompletedDailyTasksByDate(LocalDate date) {
        return taskRepository.countCompletedDailyTasksByDate(date);
    }

    @Override
    public long countExtraTasksByDate(LocalDate date) {
        return taskRepository.countExtraTasksByDate(date);
    }

    @Override
    public long countCompletedExtraTasksByDate(LocalDate date) {
        return taskRepository.countCompletedExtraTasksByDate(date);
    }

    @Override
    public long sumCompletedTaskDurationByDate(LocalDate date) {
        Long seconds = taskRepository.sumCompletedTaskDurationSecondsByDate(date);
        return seconds != null ? seconds : 0;
    }

    @Override
    public List<TaskDetailDTO> getTasksByDateAndPriorities(LocalDate date, List<TaskPriority> priorities) {
        List<Task> tasks = taskRepository.findByDateAndPriorities(date, priorities);
        return tasks.stream()
            .map(this::convertToTaskDetailDTO)
            .collect(Collectors.toList());
    }

    private TaskDetailDTO convertToTaskDetailDTO(Task task) {
        TaskDetailDTO dto = new TaskDetailDTO();
        dto.setId(task.getId());
        dto.setTitle(task.getTitle());
        dto.setDescription(task.getDescription());
        dto.setStatus(task.getStatus().name());
        dto.setPriority(task.getPriority().name());
        dto.setCreatedAt(task.getCreated_at());
        dto.setBeginAt(task.getBegin_at());
        dto.setCompletedAt(task.getCompleted_at());
        dto.setExperiencePoints(task.getExperience_points());
        dto.setFragmentReward(task.getFragment_reward());
        dto.setAnswerTags(getAnswerTags(task.getId()));
        return dto;
    }

    public double getWeeklyCompletionRate() {
        LocalDate today = LocalDate.now();
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate endOfWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        
        List<Task> weeklyTasks = taskRepository.findByCreatedAtBetweenDates(startOfWeek, endOfWeek);
        long totalTasks = weeklyTasks.size();
        if (totalTasks == 0) {
            return 0.0;
        }
        
        long completedTasks = weeklyTasks.stream()
            .filter(task -> Task.TaskStatus.已完成.equals(task.getStatus()))
            .count();
        
        return (completedTasks * 100.0) / totalTasks;
    }

    @Override
    public List<com.quwei.dto.DailyCompletionRateDTO> getWeeklyCompletionRates() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(6);
        
        List<Object[]> totalTasksList = taskRepository.countTotalTasksByDate(startDate, endDate);
        List<Object[]> completedTasksList = taskRepository.countCompletedTasksByDate(startDate, endDate);
        
        Map<LocalDate, Long> totalTasksMap = totalTasksList.stream()
            .collect(java.util.stream.Collectors.toMap(
                row -> ((java.sql.Date) row[0]).toLocalDate(),
                row -> (Long) row[1]
            ));
        
        Map<LocalDate, Long> completedTasksMap = completedTasksList.stream()
            .collect(java.util.stream.Collectors.toMap(
                row -> ((java.sql.Date) row[0]).toLocalDate(),
                row -> (Long) row[1]
            ));
        
        List<com.quwei.dto.DailyCompletionRateDTO> result = new java.util.ArrayList<>();
        
        for (int i = 0; i < 7; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            Long totalTasks = totalTasksMap.getOrDefault(currentDate, 0L);
            Long completedTasks = completedTasksMap.getOrDefault(currentDate, 0L);
            
            double completionRate = totalTasks == 0 ? 0.0 : (completedTasks * 100.0) / totalTasks;
            completionRate = Math.round(completionRate * 100) / 100.0; // 保留两位小数
            
            result.add(new com.quwei.dto.DailyCompletionRateDTO(currentDate, completionRate));
        }
        
        return result;
    }
    
    @Override
    public TaskResponseDTO getTasksByDate(LocalDate date, int page, int pageSize) {
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        Page<Task> taskPage = taskRepository.findByDate(date, pageable);

        List<TaskDTO> taskDTOs = taskPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        TaskResponseDTO responseDTO = new TaskResponseDTO();
        responseDTO.setTasks(taskDTOs);
        responseDTO.setPagination(new TaskResponseDTO.Pagination(
                taskPage.getTotalElements(),
                page,
                pageSize,
                taskPage.getTotalPages()
        ));

        return responseDTO;
    }

    @Override
    public boolean createTask(CreateTaskRequest request) {
        Task task = new Task();
        task.setTitle(request.getTitle());
        task.setDescription(request.getDescription());
        task.setPriority(Task.TaskPriority.valueOf(request.getPriority()));
        task.setExperience_points(request.getExperience_points());
        task.setFragment_reward(request.getFragment_reward());

        // 检查额外任务是否已有未完成状态
        if (task.getPriority() == Task.TaskPriority.额外任务) {
            long unfinishedExtraTasks = taskRepository.countUnfinishedExtraTasks();
            if (unfinishedExtraTasks > 0) {
                return false; // 存在未完成的额外任务，不允许创建
            }
        }

        task.setStatus(Task.TaskStatus.未完成);
        task.setCreated_at(LocalDateTime.now());
        Task savedTask = taskRepository.save(task);

        // 保存答案标签到answers表
        if (request.getAnswer_tags() != null && request.getAnswer_tags().length > 0) {
            List<Answer> answers = Arrays.stream(request.getAnswer_tags())
                .filter(tag -> tag != null && !tag.trim().isEmpty())
                .map(tag -> {
                    Answer answer = new Answer();
                    answer.setTaskId(savedTask.getId());
                    answer.setTagName(tag.trim());
                    return answer;
                })
                .collect(Collectors.toList());

            answerRepository.saveAll(answers);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean updateTask(Integer id, UpdateTaskRequest request) {
        return taskRepository.findById(id).map(task -> {
            if (request.getTitle() != null) task.setTitle(request.getTitle());
            if (request.getDescription() != null) task.setDescription(request.getDescription());
            if (request.getPriority() != null) task.setPriority(Task.TaskPriority.valueOf(request.getPriority()));
            if (request.getExperience_points() != null) task.setExperience_points(request.getExperience_points());
            if (request.getFragment_reward() != null) task.setFragment_reward(request.getFragment_reward());
            
            // 处理额外任务的答案标签
            if ("额外任务".equals(request.getPriority())) {
                // 删除现有标签
                answerRepository.deleteByTaskId(id);
                
                // 保存新标签
                if (request.getAnswer_tags() != null && !request.getAnswer_tags().isEmpty()) {
                    List<Answer> answers = request.getAnswer_tags().stream()
                        .filter(tag -> tag != null && !tag.trim().isEmpty())
                        .map(tag -> {
                            Answer answer = new Answer();
                            answer.setTaskId(id);
                            answer.setTagName(tag.trim());
                            return answer;
                        })
                        .collect(Collectors.toList());
                    
                    answerRepository.saveAll(answers);
                }
            }
            
            taskRepository.save(task);
            return true;
        }).orElse(false);
    }

    @Override
    public boolean deleteTask(Integer id) {
        if (taskRepository.existsById(id)) {
            taskRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private TaskDTO convertToDTO(Task task) {
        TaskDTO dto = new TaskDTO();
        dto.setId(task.getId());
        dto.setTitle(task.getTitle());
        dto.setDescription(task.getDescription());
        dto.setStatus(task.getStatus().name());
        dto.setPriority(task.getPriority().name());
        dto.setCreated_at(task.getCreated_at().toString());
        dto.setCompleted_at(task.getCompleted_at() != null ? task.getCompleted_at().toString() : null);
        dto.setExperience_points(task.getExperience_points());
        dto.setFragment_reward(task.getFragment_reward());
        return dto;
    }

    @Override
    public Task getTaskById(Integer id) {
        return taskRepository.findById(id).orElse(null);
    }
}