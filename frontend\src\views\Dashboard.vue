<template>
  <div>
    <el-container class="dashboard">
    <!-- 头部用户信息和进度 -->
    <el-header class="header">
      <div class="user-info">
  <div class="user-details">
    <h1>你好呀，敖丙！</h1>
    <p>当前等级：<span class="level">{{levelName}} Lv.{{currentLevel}}</span></p>
  </div>
  <el-avatar :src="userAvatar" :class="currentLevelStyle"></el-avatar>
</div>
      <div class="progress-container">
        <p>当前经验值：{{totalExperience}}/{{currentLevelMaxXP}}</p>
        <el-progress :percentage="progressPercentage" :stroke-width="8" status="success"></el-progress>
        
      </div>
    </el-header>

    <el-main>
      <!-- 今日任务表 -->
      <el-card class="task-card">
        <div class="task-header">
          <h2>今日任务表</h2>
          <div class="task-stats">
            <span>{{ tasks.length }}个任务</span>
            <span>{{ tasks.filter(task => task.status === '已完成').length }}个已完成</span>
          </div>
        </div>
        <div class="task-container">
  <button class="scroll-btn left" @click="scrollLeft">&lt;</button>
  <div class="task-list" ref="taskList">
      <el-card class="task-item" v-for="task in tasks" :key="task.id">
        <div class="task-content">
  <div class="fragment-row">
      <div class="task-fragment">
        <el-icon size="16" style="color: #1890FF;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path d="M512 128L128 512l384 384 384-384L512 128z" fill="currentColor"/></svg></el-icon>
        <span>{{ task.fragment || 0 }}个碎片</span>
      </div>
      <button class="task-action-btn" :class="getStatusClass(task.status)" @click="handleTaskButtonClick(task)" :disabled="task.status === '已完成'">
        <template v-if="task.status === '未完成'">开始任务</template>
        <template v-else-if="task.status === '进行中'">
          <span v-if="task.timerRunning">{{ formatTime(task.timerSeconds) }} ⏸️</span>
          <span v-else>{{ formatTime(task.timerSeconds) }} ▶️</span>
        </template>
        <template v-else>{{ formatDuration(task.beginAt, task.completedAt) }}</template>
      </button>
    </div>
  <div class="task-title">{{ task.title }}</div>
          <div class="task-details">{{ task.description }}</div>
        </div>
        <div class="task-footer">
          <div class="task-exp">
            <el-icon><Star /></el-icon>
            <span>{{ task.exp || '10' }}经验值</span>
          </div>
          <div class="task-tag" :class="getStatusClass(task.status)">{{ task.status }}</div>
        </div>
      </el-card>
  </div>
  <button class="scroll-btn right" @click="scrollRight">&gt;</button>
</div>
      </el-card>

      <!-- 成长和奖励区域 -->
      <div class="growth-rewards">
        <!-- 我的成长 -->
        <el-card class="growth-card">
          <h2>我的成长</h2>
          <div class="growth-sections">
            <div class="badge-section">
              <h3>碎片中心</h3>
              <div class="badge-container">
                <img :src="'/images/growth_center.jpg'" class="badge-background" alt="Background image">
                <div class="badge-grid">
                  <div class="badge" v-if="hiddenGrids < 1"></div>
                  <div class="badge" v-if="hiddenGrids < 2"></div>
                  <div class="badge" v-if="hiddenGrids < 3"></div>
                  <div class="badge" v-if="hiddenGrids < 4"></div>
                  <div class="badge" v-if="hiddenGrids < 5"></div>
                  <div class="badge" v-if="hiddenGrids < 6"></div>
                  <div class="badge" v-if="hiddenGrids < 7"></div>
                  <div class="badge" v-if="hiddenGrids < 8"></div>
                  <div class="badge" v-if="hiddenGrids < 9"></div>
                </div>
              </div>
              <p>{{ hiddenGrids }}/9碎片已点亮，再收集20个碎片可解锁新区域</p>
            </div>
            <div class="achievements">
              <h3>成就徽章</h3>
              <div class="achievement-icons">
                <div class="achievement-badge" :class="{ 'locked': !achievementStatus.completeAllDailyTasks }" :title="!achievementStatus.completeAllDailyTasks ? '完成当天所有任务' : '已完成'"><el-icon class="achievement-icon"><Trophy /></el-icon></div>
                <div class="achievement-badge" :class="{ 'locked': !achievementStatus.completeAllExtraTasks }" :title="!achievementStatus.completeAllExtraTasks ? '完成当天所有额外任务' : '已完成'"><el-icon class="achievement-icon"><Star /></el-icon></div>
                <div class="achievement-badge" :class="{ 'locked': !achievementStatus.unlockAllGridCells }" :title="!achievementStatus.unlockAllGridCells ? '解锁九宫格所有格子' : '已完成'"><el-icon class="achievement-icon"><Medal /></el-icon></div>
                <div class="achievement-badge" :class="{ 'locked': !achievementStatus.totalTaskTimeOver2Hours }" :title="!achievementStatus.totalTaskTimeOver2Hours ? '统计当天已完成任务的时间大于等于2小时' : '已完成'"><el-icon class="achievement-icon"><Coffee /></el-icon></div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 奖励兑换站 -->
        <el-card class="rewards-card">
          <h2>奖励兑换站</h2>
          <el-tabs v-model="activeTab" class="reward-tabs">
            <el-tab-pane label="奖品列表" name="奖品列表">
              <div class="reward-list">
                <div class="reward-item" v-for="reward in rewards" :key="reward.id" :class="{ 'locked': !reward.available }">
                  <div class="reward-info">
                    <h3>{{ reward.name }}</h3>
                    <p>{{ reward.fragmentCount }}碎片</p>
                  </div>
                  <el-button :type="reward.canRedeem ? 'success' : 'default'" :disabled="!reward.canRedeem" class="reward-btn" @click="handleRedeem(reward)">
                    {{ reward.canRedeem ? '可兑换' : '碎片不足' }}
                  </el-button>
                </div>
              </div>
              <div style="display: flex; justify-content: flex-end; margin-top: 15px;">
                <el-pagination
                  @current-change="handlePageChange"
                  :current-page="currentPage + 1"
                  :page-size="4"
                  layout="prev, pager, next"
                  :total="totalRewards">
                </el-pagination>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>

      <!-- 惊喜彩蛋区 -->
      <el-card class="surprise-card">
        <h3 style="text-align: left !important; margin-bottom: 15px;">惊喜彩蛋区</h3>
        <div class="content-row" style="display: flex; gap: 20px;">
          
          <div class="quiz-card" style="flex: 1; border: 1px solid #e0e0e0; background-color: #f9f9f9; padding: 20px; border-radius: 8px;">
              <div class="quiz-header">
                <h3 style="margin: 0; color: #333;">敖丙小课堂</h3>
              </div>
              <div class="quiz-content" :style="extraTaskStatus === '进行中' ? { position: 'relative' } : {}">
                <div v-if="extraTaskStatus === '进行中'" class="submitted-mask">
                  <div class="mask-content">答案已提交</div>
                </div>
                <div v-if="quizLoading" class="quiz-loading">加载中...</div>
                <div v-else-if="quizError" class="quiz-error">{{ quizError }}</div>
                <div v-else>
                  <p style="margin-bottom: 15px; color: #666;">问题：{{ currentQuestion }}</p>
                  <el-radio-group v-model="selectedAnswer" class="radio-group" style="margin-bottom: 20px;">
                    <el-radio v-for="(option, index) in options" :key="index" :label="String.fromCharCode(65 + index)" style="display: block; margin-bottom: 8px;">
                      {{ String.fromCharCode(65 + index) }}. {{ option }}
                    </el-radio>
                  </el-radio-group>
                  <el-button type="primary" @click="handleSubmit" style="width: 100%;" :disabled="!selectedAnswer || extraTaskStatus === '进行中'">提交答案</el-button>
                </div>
              </div>
            </div>
            
        </div>
      </el-card>
    </el-main>
  </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, onMounted } from 'vue';
import axios from 'axios';
import { ElRadioGroup, ElRadio, ElMessage, ElMessageBox } from 'element-plus';

// 等级与经验值逻辑
const totalExperience = ref(0);
const dailyFragments = ref(0);
const hiddenGrids = ref(0);
const currentLevel = ref(1);
const currentLevelMaxXP = ref(100);
const progressPercentage = ref(0);
const levelName = ref('黄闪小萌新');
const currentLevelStyle = ref('level-style-1'); // 默认等级样式

// 定义10个等级的经验值区间 [minXP, maxXP, 等级名称]
const levelRequirements = [
  [0, 100, '黄闪小萌新'],
  [100, 250, '探索小勇士'],
  [250, 450, '智慧小达人'],
  [450, 700, '学习小能手'],
  [700, 1000, '知识小博士'],
  [1000, 1350, '成长小先锋'],
  [1350, 1750, '毅力小冠军'],
  [1750, 2200, '全能小明星'],
  [2200, 2700, '超凡小大师'],
  [2700, Infinity, '宇宙小英雄']
];


const fetchDailyFragments = async () => {
  try {
    const response = await axios.get('/api/tasks/daily-fragments', { params: { t: new Date().getTime() } });
    dailyFragments.value = Number(response.data) || 0;
    console.log('每日碎片数量:', dailyFragments.value);
    hiddenGrids.value = Math.min(Math.floor(dailyFragments.value / 20), 9);
    console.log('计算隐藏宫格数:', hiddenGrids.value);
  } catch (error) {
    console.error('获取每日碎片失败:', error);
  }
};
const handleRedeem = async (reward) => {
  try {
    await ElMessageBox.confirm(
      `确定要兑换【${reward.name}】吗？将消耗${reward.fragmentCount}碎片`,
      '兑换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const response = await axios.post('/api/exchange-records/exchange/new', {
      reward_id: reward.id,
      fragmentsUsed: reward.fragmentCount
    });
    
    if (response.data && response.data.id) {
      ElMessage.success('兑换成功！');
      fetchRewards(); // 刷新奖品列表
    } else {
      ElMessage.error(response.data.message || '兑换失败');
    }
  } catch (error) {
    if (error.name !== 'Error') {
      // 不是确认框取消导致的错误
      console.error('兑换失败:', error);
      ElMessage.error('兑换失败，请重试');
    }
  }
};
// 获取总经验值并计算等级
const fetchTotalExperience = async () => {
  try {
    const response = await axios.get(`/api/tasks/total-experience?t=${new Date().getTime()}`);
    totalExperience.value = response.data.data || 0;
    calculateLevel();
  } catch (error) {
    console.error('获取经验值失败:', error);
  }
};

// 计算当前等级和进度
const calculateLevel = () => {
  for (let i = 0; i < levelRequirements.length; i++) {
    const [minXP, maxXP, name] = levelRequirements[i];
    if (totalExperience.value >= minXP && totalExperience.value < maxXP) {
      currentLevel.value = i + 1;
      levelName.value = name;
      currentLevelMaxXP.value = maxXP === Infinity ? 'MAX' : maxXP;
      progressPercentage.value = maxXP === Infinity ? 100 : 
        Math.round(((totalExperience.value - minXP) / (maxXP - minXP)) * 100);
      currentLevelStyle.value = `level-style-${currentLevel.value}`; // 设置当前等级样式
      break;
    }
  }
};

const achievementStatus = ref({
  completeAllDailyTasks: false,
  completeAllExtraTasks: false,
  unlockAllGridCells: false,
  totalTaskTimeOver2Hours: false
});

const fetchAchievementStatus = async () => {
  try {
    const response = await axios.get('/api/achievements/status', { params: { t: new Date().getTime() } });
    achievementStatus.value = response.data;
  } catch (error) {
    console.error('获取成就状态失败:', error);
  }
};

onMounted(() => {
  fetchTotalExperience();
  fetchDailyFragments();
  fetchAchievementStatus();
  fetchRewards();
  fetchExtraTask();
});

// 奖品数据与分页逻辑
const selectedAnswer = ref('');
const answerSubmitted = ref(false);
const currentQuestion = ref('');
const options = ref([]);
const quizLoading = ref(false);
const quizError = ref('');
const extraTaskStatus = ref(''); // 添加额外任务状态

const fetchExtraTask = async () => {
  quizLoading.value = true;
  quizError.value = '';
  try {
    const response = await axios.get('/api/tasks/today-tasks', {
      params: {
        priorities: 'extra'
      }
    });
    const task = response.data.data[0];
    if (task) {
      taskId.value = task.id;
      currentQuestion.value = task.description || '获取问题失败';
      options.value = task.answerTags || [];
      extraTaskStatus.value = task.status || ''; // 获取任务状态
    } else {
      quizError.value = '没有找到额外任务数据';
    }
  } catch (error) {
    console.error('获取额外任务失败:', error);
    if (error.response) {
      // 服务器返回了错误响应
      quizError.value = `获取问题失败: ${error.response.status} ${error.response.data?.message || error.response.statusText}`;
      console.error('API错误详情:', error.response.data);
    } else if (error.request) {
      // 请求已发送但未收到响应
      quizError.value = '获取问题失败: 服务器无响应，请检查网络连接';
    } else {
      // 请求配置错误
      quizError.value = '获取问题失败: 请求配置错误';
    }
  } finally {
    quizLoading.value = false;
  }
};
const handleSubmit = async () => {
  if (!selectedAnswer.value) {
    ElMessage.warning('请选择答案后再提交');
    return;
  }
  try {
    await ElMessageBox.confirm('是否确认提交答案？', '提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    if (taskId.value) {
      try {
        await axios.put(`/api/tasks/${taskId.value}`, {
          status: '进行中'
        });
        extraTaskStatus.value = '进行中'; // 更新本地状态
        ElMessage.success('任务已开始');

      } catch (error) {
        console.error('更新任务状态失败:', error);
        ElMessage.error('提交成功，但更新任务状态失败');
      }
    }
  } catch (error) {
    ElMessage.info('已取消提交');
  }
};
const currentPage = ref(0);
const rewards = ref([]);
const totalRewards = ref(0);
const taskId = ref(null);

const filteredRewards = computed(() => {
  const startIndex = currentPage.value * 4;
  return rewards.value.slice(startIndex, startIndex + 4);
});

const handlePageChange = (page) => {
  currentPage.value = page - 1;
  fetchRewards();
};


import { Trophy, Star, Medal, Coffee, Calendar, Clock, ArrowRight, Check } from '@element-plus/icons-vue'
console.log('Imported icons:', { Trophy, Star, Medal, Coffee, Calendar, Clock, ArrowRight, Check });

const getStatusClass = (status) => {
  const statusMap = {
    '未开始': 'status-pending',
    '进行中': 'status-progress',
    '已完成': 'status-completed'
  };
  return statusMap[status] || '';
};

// 用户头像
const userAvatar = '/head.png';

// 标签页激活状态
const activeTab = ref('奖品列表');

// 任务数据
const taskList = ref(null);

const scrollLeft = () => {
  if (taskList.value) {
    taskList.value.scrollBy({ left: -300, behavior: 'smooth' });
  }
};

const scrollRight = () => {
  if (taskList.value) {
    taskList.value.scrollBy({ left: 300, behavior: 'smooth' });
  }
};

const tasks = ref([]);
const taskLoading = ref(true);
const taskError = ref(null);
onMounted(async () => {
  try {
    const response = await axios.get('/api/tasks/today-tasks', {
      params: {
        priorities: 'basic,important'
      }
    });
    tasks.value = response.data.data.map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      status: task.status,
      statusClass: getStatusClass(task.status),
      exp: task.experiencePoints,
      fragment: task.fragmentReward,
      answerTags: task.answerTags || [],
      beginAt: task.beginAt,
      completedAt: task.completedAt,
      timerSeconds: 0,
      timerRunning: false,
      timerInterval: null
    }));
  } catch (error) {
    console.error('获取今日任务失败:', error);
    taskError.value = '获取任务失败，请刷新页面重试';
  } finally {
    taskLoading.value = false;
  }
});


const fetchRewards = async () => {
  try {
    const response = await axios.get(`/api/child/rewards?page=${currentPage.value}&size=4`);
    rewards.value = response.data.data.content;
    totalRewards.value = response.data.data.totalElements;
  } catch (error) {
    console.error('获取奖品列表失败:', error);
    ElMessage.error('获取奖品列表失败');
  }
};
// 格式化时间为 MM:SS 格式
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};
const formatDuration = (beginAt, completedAt) => {
  if (!beginAt || !completedAt) return '00:00:00';
  const start = new Date(beginAt);
  const end = new Date(completedAt);
  const durationMs = end - start;
  const totalSeconds = Math.floor(durationMs / 1000);
  const hours = Math.floor(totalSeconds / 3600).toString().padStart(2, '0');
  const minutes = Math.floor((totalSeconds % 3600) / 60).toString().padStart(2, '0');
  const seconds = (totalSeconds % 60).toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
};
// 处理任务按钮点击事件
const handleTaskButtonClick = async (task) => {
  if (task.status === '未完成') {
    // 开始任务 - 保持原逻辑，调用后端
    task.status = '进行中';
    task.statusClass = getStatusClass(task.status);
    task.timerRunning = true;
    task.timerSeconds = 0;
    task.timerInterval = setInterval(() => {
      task.timerSeconds++;
    }, 1000);
    // 调用后端API更新状态为进行中
    try {
      await axios.put(`/api/tasks/${task.id}`, { status: '进行中' });
      ElMessage.success('任务已开始');
    } catch (error) {
      console.error('更新任务状态失败:', error);
      ElMessage.error('开始任务失败，请重试');
      task.status = '未完成';
      task.statusClass = getStatusClass(task.status);
      task.timerRunning = false;
      clearInterval(task.timerInterval);
    }
  } else if (task.status === '进行中') {
    // 暂停/继续计时器 - 纯前端操作，不调用后端
    if (task.timerRunning) {
      // 暂停计时器
      clearInterval(task.timerInterval);
      task.timerRunning = false;
      ElMessage.info('计时器已暂停');
    } else {
      // 继续计时器
      task.timerRunning = true;
      task.timerInterval = setInterval(() => {
        task.timerSeconds++;
      }, 1000);
      ElMessage.info('计时器已继续');
    }
  }
};

// 组件卸载时清除所有计时器
onBeforeUnmount(() => {
  tasks.value.forEach(task => {
    if (task.timerInterval) {
      clearInterval(task.timerInterval);
    }
  });
});
</script>

<style scoped>
/* 基础样式 */
.dashboard {
  width: 100%;
  max-width: 90%;
  margin: 0 auto;
  min-height: 100vh;
}

/* 页面特有样式 - 优化头部用户信息区样式 */
.header {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background-image: url('/user.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: #333333;
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  min-height: 280px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

::v-deep .el-main {
  padding: 0 !important;
  max-width: none !important;
}

::v-deep .el-container {
  max-width: 100% !important;
}

::v-deep .el-card__body {
  height: auto;
  min-height: auto !important;
}

.el-main {
  padding: 0;
}

/* 头部样式 */
.header {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background-color: #ffffff;
  color: #333333;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  min-height: 240px;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 16px;
  margin-bottom: 20px;
}

.user-info .el-avatar {
  width: 100px;
  height: 100px;
  border: 3px solid white;
}

.user-details h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333333;
}

.level {
  background: linear-gradient(90deg, #ffd700, #ffa500);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  color: #606266;
  font-weight: 500;
}

.progress-container {
  width: 100%;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

::v-deep .el-progress-bar__inner {
  background-color: #409eff !important;
}

.progress-container p {
  margin: 0 0 8px 0;
  display: flex;
  justify-content: space-between;
}

/* 任务卡片样式 */
.task-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-header h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.task-stats {
  color: #606266;
  font-size: 12px;
}

.task-stats span:not(:last-child) {
  margin-right: 16px;
}

.task-container {
  width: 100%;
  overflow-x: auto;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: #409eff transparent;
}

.task-container::-webkit-scrollbar {
  height: 6px;
}

.task-container::-webkit-scrollbar-thumb {
  background-color: #409eff;
  border-radius: 3px;
}

.task-container::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: transparent;
  color: #409eff;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.scroll-btn:hover {
  background: white;
}

/* 答案提交遮罩层样式 */
.quiz-content {
  position: relative;
}

.submitted-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.mask-content {
  color: white;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  padding: 20px;
}

.scroll-btn:hover {
  color: #337ecc;
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.scroll-btn.left {
  left: 10px;
}

.scroll-btn.right {
  right: 10px;
}

.task-list {
  display: flex;
  gap: 16px;
  padding: 8px 0;
  overflow-x: auto;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
}

.task-item {
  min-width: calc(25% - 12px);
  scroll-snap-align: start;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-height: 200px;
}

.task-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.fragment-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;
  width: 100%;
}

.task-fragment {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #1890FF;
  font-size: 12px;
  padding: 4px 12px;
  background-color: #E6F7FF;
  border-radius: 16px;
}

.task-action-btn {
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}
/* Removed margin-right as gap is now used */
.task-fragment .el-icon {
  font-size: 16px;
}
.task-title {
  font-weight: bold;
  font-size: 16px;
  text-align: left;
}

.task-details {
  font-size: 14px;
  color: #666;
  text-align: left;
  height: 60px;
  overflow-y: auto;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.task-action-btn {
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.task-action-btn:not(:disabled) {
  background-color: #409eff;
  color: white;
}

.task-action-btn:not(:disabled):hover {
  background-color: #337ecc;
}

.task-action-btn:disabled {
  background-color: #f5f5f5;
  color: #c0c4cc;
  cursor: not-allowed;
}

.task-action-btn.status-pending {
  background-color: #409eff !important;
}

.task-action-btn.status-progress {
  background-color: #e6a23c !important;
}

.task-action-btn.status-completed { background-color: #67c23a !important; color: white !important; }

.content-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  padding: 15px;
}

.surprise-content, .history-achievements {
  flex: 1;
  min-width: 0;
}

.task-exp {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #FFA500;
  padding: 4px 12px;
  border-radius: 16px;
  background: #fff3e0;
  font-size: 14px;
}

.task-exp i {
  font-size: 18px;
  color: #FFC107;
}
.task-exp span::before {
  content: '+ ';
  font-weight: bold;
}

.task-tag {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 12px;
}

.status-completed {
  background: #E8F5E9;
  color: #4CAF50;
}

.status-progress {
  background: #FFF8E1;
  color: #FFC107;
}

.status-pending {
  background: #FFEBEE;
  color: #F44336;
}

.task-item {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.task-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.task-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
}

.task-info .el-checkbox {
  margin-top: 4px;
}

.task-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.task-details p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f7f8fa;
  font-size: 12px;
}

.task-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status-completed {
  background-color: #f0f9eb;
  color: #52c41a;
}

.status-progress {
  background-color: #fff7e8;
  color: #fa8c16;
}

.status-pending {
  background-color: #f2f3f5;
  color: #909399;
}

/* 成长和奖励区域 */
.growth-rewards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

/* 平板和移动端响应式布局 */
@media (max-width: 1024px) {
  .dashboard {
    max-width: 100%;
    padding: 0 10px;
  }

  .header {
    padding: 20px;
    min-height: 200px;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .user-info .el-avatar {
    width: 80px;
    height: 80px;
  }

  .user-details h1 {
    font-size: 20px;
  }

  .growth-rewards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .task-item {
    min-width: auto;
    width: 100%;
  }

  .task-list {
    flex-direction: column;
    gap: 16px;
  }

  .content-row {
    flex-direction: column;
  }

  .task-container {
    overflow-x: auto;
  }

  .scroll-btn {
    display: none;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0 5px;
  }

  .header {
    padding: 15px;
    min-height: 180px;
  }

  .user-details h1 {
    font-size: 18px;
  }

  .task-header h2 {
    font-size: 18px;
  }

  .grid-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .grid-item {
    height: 60px;
  }
}

.growth-card, .rewards-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.growth-card h2, .growth-card h3 {
  text-align: left;
}

.growth-card h2, .rewards-card h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #303133;
}

.growth-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.badge-section h3, .achievements h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.badge-container {
  position: relative;
  width: 100%;
  max-width: 300px;
  aspect-ratio: 1/1;
  margin: 0 auto 16px;
  overflow: hidden;
  border: 3px solid #409eff;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.badge-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(64, 158, 255, 0.3);
}

.badge-background {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.badge-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 0;
  position: relative;
  width: 100%;
  height: 100%;
  border-collapse: collapse;
}

.badge {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  transition: opacity 0.3s ease;
}

.badge.completed {
  opacity: 0;
}

.badge {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
  margin: 0;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
  position: relative;
}

.unlock-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  width: 80%;
}

.badge.completed {
  background-color: #409eff;
  box-shadow: 0 0 12px rgba(64, 158, 255, 0.4);
  border-color: #409eff;
}

.badge-section p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.achievement-icons {
  display: flex;
  gap: 20px;
}

.achievement-icons { display: flex; justify-content: space-around; }
.achievement-badge { width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #e6f7ff 0%, #b3e0ff 100%); border-radius: 12px; box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2); transition: all 0.3s ease; }

.achievement-badge:hover { transform: translateY(-5px); box-shadow: 0 6px 12px rgba(64, 158, 255, 0.3); }

.achievement-badge.locked { background: linear-gradient(135deg, #f0f0f0 0%, #d9d9d9 100%); opacity: 0.7; }
.achievement-icon {
  font-size: 36px;
  color: #409eff;
}

.achievement-icon.locked {
  color: #c0c6cc;
}

.reward-tabs {
  margin-top: 16px;
}

.reward-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.reward-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f7f8fa;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.reward-item:hover {
  background-color: #f0f2f5;
}

.reward-item.locked {
  opacity: 0.7;
}

.reward-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.reward-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.reward-btn {
  padding: 6px 16px;
}

/* 惊喜彩蛋区 */
.surprise-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.surprise-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.surprise-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.surprise-text p {
  margin: 0 0 8px 0;
  color: #606266;
}

.surprise-image {
  text-align: center;
}

.surprise-image img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 8px;
}

.history-achievements {
  padding: 20px;
}

.history-achievements h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.achievement-record {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px dashed #ebeef5;
}

.achievement-record:last-child {
  border-bottom: none;
}

.achievement-badge {
  padding: 4px 12px;
}

.achievement-date {
  color: #909399;
  font-size: 14px;
}

/* 等级永久样式 */
.level-style-1 {
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
  border: 2px solid #FFD700;
}
.level-style-2 {
  box-shadow: 0 0 10px rgba(123, 104, 238, 0.6);
  border: 2px solid #7B68EE;
}
.level-style-3 {
  box-shadow: 0 0 12px rgba(70, 130, 180, 0.7);
  border: 2px solid #4682B4;
}
.level-style-4 {
  box-shadow: 0 0 14px rgba(60, 179, 113, 0.7);
  border: 2px solid #3CB371;
}
.level-style-5 {
  box-shadow: 0 0 16px rgba(255, 165, 0, 0.7);
  border: 2px solid #FFA500;
}
.level-style-6 {
  box-shadow: 0 0 18px rgba(255, 99, 71, 0.7);
  border: 2px solid #FF6347;
}
.level-style-7 {
  box-shadow: 0 0 20px rgba(138, 43, 226, 0.7);
  border: 2px solid #8A2BE2;
}
.level-style-8 {
  box-shadow: 0 0 22px rgba(255, 20, 147, 0.7);
  border: 2px solid #FF1493;
}
.level-style-9 {
  box-shadow: 0 0 24px rgba(0, 191, 255, 0.7);
  border: 2px solid #00BFFF;
}
.level-style-10 {
  box-shadow: 0 0 26px rgba(255, 215, 0, 0.8), 0 0 36px rgba(255, 215, 0, 0.5);
  border: 3px solid #FFD700;
  background: radial-gradient(circle at center, rgba(255,215,0,0.2) 0%, transparent 70%);
}

@keyframes levelUp {
  0% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7); transform: scale(1); }
  50% { box-shadow: 0 0 0 20px rgba(255, 215, 0, 0); transform: scale(1.1); }
  100% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0); transform: scale(1); }
}
</style>

