package com.quwei.service.impl;

import com.quwei.service.AchievementService;
import com.quwei.service.TaskService;
import com.quwei.service.GridService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public class AchievementServiceImpl implements AchievementService {

    @Autowired
    private TaskService taskService;

    @Autowired
    private GridService gridService;

    @Override
    public boolean checkAllDailyTasksCompleted(LocalDate date) {
        // 获取当天所有基础任务
        long totalDailyTasks = taskService.countDailyTasksByDate(date);
        if (totalDailyTasks == 0) {
            return false;
        }
        // 获取已完成的基础任务数量
        long completedDailyTasks = taskService.countCompletedDailyTasksByDate(date);
        return totalDailyTasks > 0 && totalDailyTasks == completedDailyTasks;
    }

    @Override
    public boolean checkAllExtraTasksCompleted(LocalDate date) {
        // 获取当天所有额外任务
        long totalExtraTasks = taskService.countExtraTasksByDate(date);
        if (totalExtraTasks == 0) {
            return false;
        }
        // 获取已完成的额外任务数量
        long completedExtraTasks = taskService.countCompletedExtraTasksByDate(date);
        return totalExtraTasks > 0 && totalExtraTasks == completedExtraTasks;
    }

    @Override
    public boolean checkAllGridCellsUnlocked() {
        // 检查九宫格是否全部解锁（9个格子）
        int unlockedCells = gridService.countUnlockedGridCells();
        return unlockedCells >= 9;
    }

    @Override
    public boolean checkTotalTaskTimeOver2Hours(LocalDate date) {
        // 统计当天已完成任务的总时间（秒）
        long totalSeconds = taskService.sumCompletedTaskDurationByDate(date);
        // 2小时 = 7200秒
        return totalSeconds >= 7200;
    }
}