package com.quwei.controller;

import com.quwei.dto.ExchangeRecordResponseDTO;
import com.quwei.dto.ExchangeRecordDTO;
import com.quwei.service.ExchangeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/exchange-records")
public class ExchangeRecordController {

    private final ExchangeRecordService exchangeRecordService;

    @Autowired
    public ExchangeRecordController(ExchangeRecordService exchangeRecordService) {
        this.exchangeRecordService = exchangeRecordService;
    }

    @GetMapping
    public ResponseEntity<ExchangeRecordResponseDTO> getExchangeRecordsByUserId(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "2") int page_size) {
        return ResponseEntity.ok(exchangeRecordService.getExchangeRecordsByUserId(page, page_size));
    }

    @PostMapping("/exchange/new")
    public ResponseEntity<?> createNewExchangeRecord( @RequestBody ExchangeRecordDTO request) {
        ExchangeRecordDTO exchangeRecord = exchangeRecordService.createNewExchangeRecord(request);
        return ResponseEntity.ok(exchangeRecord);
    }
}
