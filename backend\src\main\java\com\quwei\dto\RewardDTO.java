package com.quwei.dto;

import java.time.LocalDateTime;

public class RewardDTO {
    private Integer id;
    private String name;
    private String description;
    private Integer fragment_count;
    private Integer stock;
    private LocalDateTime created_at;

    // 添加无参构造函数
    public RewardDTO() {
    }

    // 添加查询所需的构造函数
    public RewardDTO(Integer id, String name, Integer fragment_count) {
        this.id = id;
        this.name = name;
        this.fragment_count = fragment_count;
    }

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public Integer getFragment_count() { return fragment_count; }
    public void setFragment_count(Integer fragment_count) { this.fragment_count = fragment_count; }
    public Integer getStock() { return stock; }
    public void setStock(Integer stock) { this.stock = stock; }
    public LocalDateTime getCreated_at() { return created_at; }
    public void setCreated_at(LocalDateTime created_at) { this.created_at = created_at; }
}