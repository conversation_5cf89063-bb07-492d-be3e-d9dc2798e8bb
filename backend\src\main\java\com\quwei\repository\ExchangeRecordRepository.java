package com.quwei.repository;

import com.quwei.entity.ExchangeRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ExchangeRecordRepository extends JpaRepository<ExchangeRecord, Integer> {
    @Query("SELECT SUM(e.fragmentsUsed) FROM ExchangeRecord e")
    Integer sumFragmentsUsed();
}