package com.quwei.repository;

import com.quwei.entity.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.time.LocalDate;
import java.util.List;
import com.quwei.entity.Task.TaskPriority;

public interface TaskRepository extends JpaRepository<Task, Integer> {
    @Query("SELECT t FROM Task t WHERE FUNCTION('DATE', t.created_at) = :date")
    Page<Task> findByDate(@Param("date") LocalDate date, Pageable pageable);
    
    @Query("SELECT t FROM Task t WHERE FUNCTION('DATE', t.created_at) BETWEEN :startDate AND :endDate")
    List<Task> findByCreatedAtBetweenDates(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("SELECT SUM(t.fragment_reward) FROM Task t WHERE t.status = '已完成'")
    Integer sumAllFragments();

    @Query("SELECT SUM(t.experience_points) FROM Task t WHERE t.status = '已完成'")
    Integer sumTotalExperience();

    @Query("SELECT SUM(t.fragment_reward) FROM Task t WHERE t.priority IN ('基础任务', '重点任务') AND t.status = '已完成'")
    Integer sumDailyFragments();

    @Query("SELECT SUM(t.fragment_reward) FROM Task t WHERE t.priority = '额外任务' AND t.status = '已完成'")
    Integer sumExtraFragments();

    @Query("SELECT t FROM Task t WHERE FUNCTION('DATE', t.created_at) = :date AND t.priority IN :priorities")
    List<Task> findByDateAndPriorities(@Param("date") LocalDate date, @Param("priorities") List<TaskPriority> priorities);

    @Query("SELECT SUM(t.fragment_reward) FROM Task t WHERE t.priority IN ('基础任务', '重点任务') AND t.status = '已完成' AND FUNCTION('DATE', t.created_at) = :date")
    Integer sumDailyFragmentsByDate(@Param("date") LocalDate date);

    @Query("SELECT SUM(t.fragment_reward) FROM Task t WHERE t.priority = '额外任务' AND t.status = '已完成' AND FUNCTION('DATE', t.created_at) = :date")
    Integer sumExtraFragmentsByDate(@Param("date") LocalDate date);

    @Query("SELECT SUM(FUNCTION('TIMESTAMPDIFF', MINUTE, t.begin_at, t.completed_at)) FROM Task t WHERE t.status = '已完成' AND FUNCTION('DATE', t.completed_at) = :date")
    Integer sumDailyTaskDuration(LocalDate date);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.priority IN ('基础任务', '重点任务') AND FUNCTION('DATE', t.created_at) = :date")
    long countDailyTasksByDate(@Param("date") LocalDate date);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.priority IN ('基础任务', '重点任务') AND t.status = '已完成' AND FUNCTION('DATE', t.created_at) = :date")
    long countCompletedDailyTasksByDate(@Param("date") LocalDate date);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.priority = '额外任务' AND FUNCTION('DATE', t.created_at) = :date")
    long countExtraTasksByDate(@Param("date") LocalDate date);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.priority = '额外任务' AND t.status = '已完成' AND FUNCTION('DATE', t.created_at) = :date")
    long countCompletedExtraTasksByDate(@Param("date") LocalDate date);

    @Query("SELECT COUNT(t) FROM Task t WHERE t.priority = '额外任务' AND t.status = '未完成'")
    long countUnfinishedExtraTasks();

    @Query("SELECT SUM(FUNCTION('TIMESTAMPDIFF', SECOND, t.begin_at, t.completed_at)) FROM Task t WHERE t.status = '已完成' AND FUNCTION('DATE', t.completed_at) = :date")
    Long sumCompletedTaskDurationSecondsByDate(@Param("date") LocalDate date);

    @Query("SELECT FUNCTION('DATE', t.created_at) AS taskDate, COUNT(t) AS totalTasks FROM Task t WHERE FUNCTION('DATE', t.created_at) BETWEEN :startDate AND :endDate GROUP BY FUNCTION('DATE', t.created_at)")
    List<Object[]> countTotalTasksByDate(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("SELECT FUNCTION('DATE', t.created_at) AS taskDate, COUNT(t) AS completedTasks FROM Task t WHERE t.status = '已完成' AND FUNCTION('DATE', t.created_at) BETWEEN :startDate AND :endDate GROUP BY FUNCTION('DATE', t.created_at)")
    List<Object[]> countCompletedTasksByDate(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}