package com.quwei.repository;

import com.quwei.entity.Reward;
import com.quwei.dto.RewardDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface RewardRepository extends JpaRepository<Reward, Integer> {
    // 支持分页和排序的查询方法
    @Query("SELECT new com.quwei.dto.RewardDTO(r.id, r.name, r.fragmentCount) FROM Reward r")
    Page<RewardDTO> findAllRewardDTO(Pageable pageable);

    // 查询库存大于0的奖品，用于孩子端奖励兑换站
    @Query("SELECT new com.quwei.dto.RewardDTO(r.id, r.name, r.fragmentCount) FROM Reward r WHERE r.stock > 0")
    Page<RewardDTO> findAvailableRewardDTO(Pageable pageable);

    @Override
    Page<Reward> findAll(Pageable pageable);
}