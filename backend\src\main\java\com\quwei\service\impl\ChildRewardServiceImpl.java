package com.quwei.service.impl;

import com.quwei.dto.ChildRewardDTO;
import com.quwei.dto.RewardDTO;
import com.quwei.repository.ExchangeRecordRepository;
import com.quwei.repository.RewardRepository;
import com.quwei.repository.TaskRepository;
import com.quwei.service.ChildRewardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class ChildRewardServiceImpl implements ChildRewardService {
    private final RewardRepository rewardRepository;
    private final TaskRepository taskRepository;
    private final ExchangeRecordRepository exchangeRecordRepository;

    @Autowired
    public ChildRewardServiceImpl(RewardRepository rewardRepository, TaskRepository taskRepository, ExchangeRecordRepository exchangeRecordRepository) {
        this.rewardRepository = rewardRepository;
        this.taskRepository = taskRepository;
        this.exchangeRecordRepository = exchangeRecordRepository;
    }

    @Override
    public Page<ChildRewardDTO> getPaginatedRewards(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<RewardDTO> rewardPage = rewardRepository.findAvailableRewardDTO(pageable);
        Integer remainingFragments = calculateRemainingFragments();

        return rewardPage.map(reward -> new ChildRewardDTO(
            reward.getId(),
            reward.getName(),
            reward.getFragment_count(),
            remainingFragments >= reward.getFragment_count()
        ));
    }

    @Override
    public Integer calculateRemainingFragments() {
        Integer earnedFragments = taskRepository.sumAllFragments() != null ? taskRepository.sumAllFragments() : 0;
        Integer usedFragments = exchangeRecordRepository.sumFragmentsUsed() != null ? exchangeRecordRepository.sumFragmentsUsed() : 0;
        return earnedFragments - usedFragments;
    }
}