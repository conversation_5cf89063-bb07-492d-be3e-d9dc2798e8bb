package com.quwei.controller;

import com.quwei.dto.ApiResponse;
import com.quwei.dto.CreateRewardRequest;
import com.quwei.dto.RewardResponseDTO;
import com.quwei.service.RewardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/rewards")
public class RewardController {
    private static final Logger logger = LoggerFactory.getLogger(RewardController.class);
    private final RewardService rewardService;

    @Autowired
    public RewardController(RewardService rewardService) {
        this.rewardService = rewardService;
    }

    @GetMapping
    public ResponseEntity<ApiResponse<RewardResponseDTO>> getRewards(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "4") int page_size,
            @RequestParam(defaultValue = "id,asc") String sort) {
        try {
            int adjustedPage = Math.max(0, page - 1);
RewardResponseDTO data = rewardService.getRewards(adjustedPage, page_size, sort);
            return ResponseEntity.ok(ApiResponse.success(data));
        } catch (Exception e) {
            logger.error("Failed to get rewards", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "获取奖励列表失败: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> updateReward(
            @PathVariable Integer id,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Integer fragment_count,
            @RequestParam(required = false) Integer stock) {
        try {
            boolean success = rewardService.updateReward(id, name, description, fragment_count, stock);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success(null, "奖励已更新"));
            } else {
                return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(404, "奖励不存在"));
            }
        } catch (Exception e) {
            logger.error("Failed to update reward", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "更新奖励失败: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteReward(@PathVariable Integer id) {
        try {
            boolean success = rewardService.deleteReward(id);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success(null, "奖励已删除"));
            } else {
                return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(404, "奖励不存在"));
            }
        } catch (Exception e) {
            logger.error("Failed to delete reward", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "删除奖励失败: " + e.getMessage()));
        }
    }

    @PostMapping(consumes = "application/json")
    public ResponseEntity<ApiResponse<Void>> createReward(@RequestBody CreateRewardRequest request) {
        try {
            boolean success = rewardService.createReward(
                request.getName(),
                request.getDescription(),
                request.getFragment_count(),
                request.getStock()
            );
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success(null, "奖励已创建"));
            } else {
                return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(500, "创建奖励失败"));
            }
        } catch (Exception e) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "创建奖励时发生错误: " + e.getMessage()));
        }
    }
}