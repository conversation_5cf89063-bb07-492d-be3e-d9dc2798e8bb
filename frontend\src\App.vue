<script setup>
import AppHeader from '@/components/AppHeader.vue';
</script>

<template>
  <div class="app-container">
    <app-header></app-header>
    <router-view />
  </div>
</template>

<style>
.app-container {
  width: 100%;
  margin: 0;
  padding: 60px 0 0 0;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 平板适配 */
@media (max-width: 1024px) {
  .app-container {
    padding: 60px 10px 0 10px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 60px 5px 0 5px;
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
  background-color: #f5f7fa;
}


</style>
