package com.quwei.dto;

public class TaskDTO {
    private Integer id;
    private String title;
    private String description;
    private String status;
    private String priority;
    private String created_at;
    private String completed_at;
    private Integer experience_points;
    private Integer fragment_reward;

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public String getPriority() { return priority; }
    public void setPriority(String priority) { this.priority = priority; }
    public String getCreated_at() { return created_at; }
    public void setCreated_at(String created_at) { this.created_at = created_at; }
    public String getCompleted_at() { return completed_at; }
    public void setCompleted_at(String completed_at) { this.completed_at = completed_at; }
    public Integer getExperience_points() { return experience_points; }
    public void setExperience_points(Integer experience_points) { this.experience_points = experience_points; }
    public Integer getFragment_reward() { return fragment_reward; }
    public void setFragment_reward(Integer fragment_reward) { this.fragment_reward = fragment_reward; }
}