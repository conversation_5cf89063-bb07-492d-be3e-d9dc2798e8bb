package com.quwei.service;

import com.quwei.dto.TaskResponseDTO;
import com.quwei.dto.CreateTaskRequest;
import com.quwei.dto.TodayFragmentStatsDTO;
import java.time.LocalDate;
import java.util.List;
import com.quwei.entity.Task;
import com.quwei.entity.Task.TaskPriority;
import com.quwei.dto.TaskDetailDTO;

public interface TaskService {
    double getWeeklyCompletionRate();

    Integer getTotalFragments();

    Integer getTotalExperience();

    Integer getDailyFragments();

    Integer getDailyFragments(LocalDate date);
    
    Integer getExtraFragments();

    Integer getDailyTaskDuration(LocalDate date);

    long countDailyTasksByDate(LocalDate date);
    long countCompletedDailyTasksByDate(LocalDate date);
    long countExtraTasksByDate(LocalDate date);
    long countCompletedExtraTasksByDate(LocalDate date);
    long sumCompletedTaskDurationByDate(LocalDate date);

    TaskResponseDTO getTasksByDate(LocalDate date, int page, int pageSize);
    boolean createTask(CreateTaskRequest request);
    boolean updateTask(Integer id, com.quwei.dto.UpdateTaskRequest request);
    boolean deleteTask(Integer id);
    List<String> getAnswerTags(Integer taskId);

    List<com.quwei.dto.DailyCompletionRateDTO> getWeeklyCompletionRates();

    TodayFragmentStatsDTO getTodayFragmentStats();

    List<TaskDetailDTO> getTasksByDateAndPriorities(LocalDate date, List<TaskPriority> priorities);
    Task getTaskById(Integer id);
}