package com.quwei.service.impl;

import com.quwei.service.GridService;
import com.quwei.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GridServiceImpl implements GridService {

    private final TaskService taskService;

    @Autowired
    public GridServiceImpl(TaskService taskService) {
        this.taskService = taskService;
    }

    @Override
    public int countUnlockedGridCells() {
        int totalFragments = taskService.getTotalFragments();
        return Math.min(totalFragments / 20, 9);
    }
}