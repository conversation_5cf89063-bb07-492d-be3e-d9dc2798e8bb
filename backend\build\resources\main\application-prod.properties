spring.application.name=quwei

# 生产环境数据库连接配置
spring.datasource.url=******************************************************************************************
spring.datasource.username=root
spring.datasource.password=QuWei@2025!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# 启用JPA repositories
spring.data.jpa.repositories.enabled=true
spring.jpa.repositories.enabled=true

# 服务器配置
server.port=8080

# 日志配置
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
logging.level.org.springframework.data=DEBUG

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# CORS配置
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*

# 等待数据库连接
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.initialization-fail-timeout=60000
