import { createRouter, createWebHistory } from 'vue-router';
import Dashboard from '../views/Dashboard.vue';

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/parent',
    name: 'ParentDashboard',
    component: () => import('../views/ParentDashboard.vue')
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;