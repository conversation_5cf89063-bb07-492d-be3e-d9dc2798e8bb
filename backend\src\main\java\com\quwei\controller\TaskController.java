package com.quwei.controller;

import com.quwei.dto.TaskResponseDTO;
import com.quwei.dto.FragmentStatsDTO;
import org.springframework.web.bind.annotation.RequestMethod;
import com.quwei.dto.UpdateTaskRequest;
import com.quwei.dto.CreateTaskRequest;
import com.quwei.dto.ApiResponse;
import com.quwei.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Arrays;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import com.quwei.entity.Task;
import com.quwei.dto.TaskDetailDTO;
import java.util.List;
import com.quwei.dto.ApiResponse;
import com.quwei.dto.DailyCompletionRateDTO;
import com.quwei.dto.TodayFragmentStatsDTO;
import com.quwei.service.TaskService;
import java.time.LocalDateTime;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@RestController
@RequestMapping("/api/tasks")
public class TaskController {

    private final TaskService taskService;

    @Autowired
    public TaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @GetMapping("/weekly-completion-rate")
    public ResponseEntity<Double> getWeeklyCompletionRate() {
        double completionRate = taskService.getWeeklyCompletionRate();
        return ResponseEntity.ok(completionRate);
    }

    @GetMapping("/weekly-completion-rates")
    public ResponseEntity<List<DailyCompletionRateDTO>> getWeeklyCompletionRates() {
        return ResponseEntity.ok(taskService.getWeeklyCompletionRates());
    }

    @GetMapping("/fragment-stats")
    public ResponseEntity<FragmentStatsDTO> getFragmentStats() {
        Integer total = taskService.getTotalFragments();
        Integer daily = taskService.getDailyFragments();
        Integer extra = taskService.getExtraFragments();
        FragmentStatsDTO stats = new FragmentStatsDTO(total, daily, extra);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/daily-fragments")
    public ResponseEntity<Integer> getDailyFragments(@RequestParam(required = false) LocalDate date) {
        LocalDate queryDate = (date == null) ? LocalDate.now() : date;
        int totalFragments = taskService.getDailyFragments(queryDate);
        return ResponseEntity.ok(totalFragments);
    }
    

    @GetMapping("/total-experience")
    public ResponseEntity<ApiResponse<Integer>> getTotalExperience() {
        try {
            Integer experience = taskService.getTotalExperience();
            return ResponseEntity.ok()
                .header("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0")
                .body(ApiResponse.success(experience, "获取总经验值成功"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "获取总经验值失败: " + e.getMessage()));
        }
    }

    @GetMapping("/today-fragment-stats")
    public ResponseEntity<TodayFragmentStatsDTO> getTodayFragmentStats() {
        TodayFragmentStatsDTO stats = taskService.getTodayFragmentStats();
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/daily-task-duration")
    public ResponseEntity<String> getDailyTaskDuration(@RequestParam(required = false) LocalDate date) {
        LocalDate queryDate = (date == null) ? LocalDate.now() : date;
        int totalMinutes = taskService.getDailyTaskDuration(queryDate);
        int hours = totalMinutes / 60;
        int minutes = totalMinutes % 60;
        return ResponseEntity.ok(String.format("%02d:%02d", hours, minutes));
    }
    
    @GetMapping
    public ResponseEntity<TaskResponseDTO> getTasks(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "8") int page_size) {
        LocalDate queryDate = (date == null) ? LocalDate.now() : date;
        return ResponseEntity.ok(taskService.getTasksByDate(queryDate, page, page_size));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> updateTask(
            @PathVariable Integer id,
            @RequestBody UpdateTaskRequest request) {

        try {
            // 添加状态更新逻辑
            if (request.getStatus() != null) {
                Task task = taskService.getTaskById(id);
                if (task != null) {
                    // 状态从未完成变为进行中时设置begin_at
                    if ("进行中".equals(request.getStatus()) && Task.TaskStatus.未完成.equals(task.getStatus())) {
                        task.setBegin_at(LocalDateTime.now());
                    }
                    // 状态从进行中变为已完成时设置completed_at
                    else if ("已完成".equals(request.getStatus()) && Task.TaskStatus.进行中.equals(task.getStatus())) {
                        task.setCompleted_at(LocalDateTime.now());
                    }
                    task.setStatus(Task.TaskStatus.valueOf(request.getStatus()));
                }
            }
            boolean success = taskService.updateTask(id, request);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success(null, "任务更新成功"));
            } else {
                return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(404, "任务不存在"));
            }
        } catch (Exception e) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "更新任务时发生错误: " + e.getMessage()));
        }
    }

    @PostMapping(consumes = "application/json")
    public ResponseEntity<ApiResponse<Void>> createTask(@RequestBody CreateTaskRequest request) {
        try {
            boolean success = taskService.createTask(request);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success(null, "任务创建成功"));
            } else {
                return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(500, "任务创建失败"));
            }
        } catch (Exception e) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "创建任务时发生错误: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteTask(@PathVariable Integer id) {
        boolean success = taskService.deleteTask(id);
        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("message", success ? "任务已删除" : "任务不存在");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}/answer-tags")
    public ResponseEntity<ApiResponse<List<String>>> getAnswerTags(@PathVariable Integer id) {
        try {
            List<String> answerTags = taskService.getAnswerTags(id);
            return ResponseEntity.ok(ApiResponse.success(answerTags, "获取答案标签成功"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "获取答案标签失败: " + e.getMessage()));
        }
    }

    @GetMapping("/today-tasks")
    public ResponseEntity<ApiResponse<List<TaskDetailDTO>>> getTodayTasks(@RequestParam String priorities) {
        try {
            Map<String, String> priorityMap = new HashMap<>();
            priorityMap.put("basic", "基础任务");
            priorityMap.put("important", "重点任务");
            priorityMap.put("extra", "额外任务");
            List<Task.TaskPriority> taskPriorities = Arrays.stream(priorities.split(","))
                .map(priority -> priorityMap.getOrDefault(priority.trim(), priority))
                .map(priority -> Task.TaskPriority.valueOf(priority))
                .collect(Collectors.toList());
            LocalDate today = LocalDate.now();
            List<TaskDetailDTO> tasks = taskService.getTasksByDateAndPriorities(today, taskPriorities);
            return ResponseEntity.ok()
                .header("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0")
                .body(ApiResponse.success(tasks, "获取今日任务成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(400, "无效的任务类型: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(500, "获取今日任务失败: " + e.getMessage()));
        }
    }
}