package com.quwei.dto;

import java.time.LocalDateTime;

public class ExchangeRecordDTO {
    private Integer id;
    private Integer reward_id;
    private LocalDateTime exchange_time;
    private Integer fragmentsUsed;
    private String rewardName;

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getReward_id() { return reward_id; }
    public void setReward_id(Integer reward_id) { this.reward_id = reward_id; }
    public LocalDateTime getExchange_time() { return exchange_time; }
    public void setExchange_time(LocalDateTime exchange_time) { this.exchange_time = exchange_time; }
    public Integer getFragmentsUsed() { return fragmentsUsed; }
    public void setFragmentsUsed(Integer fragmentsUsed) { this.fragmentsUsed = fragmentsUsed; }
    public String getRewardName() { return rewardName; }
    public void setRewardName(String rewardName) { this.rewardName = rewardName; }
}
