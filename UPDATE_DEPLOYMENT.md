# 云服务更新部署指南

## 📋 本次更新内容

### 1. 敖丙小课堂功能优化
- ✅ 修改答案提交后任务状态从"已完成"改为"进行中"
- ✅ 遮罩层显示条件改为基于真实任务状态（防止刷新后重复编辑）
- ✅ 遮罩层文字显示为"答案已提交"

### 2. 响应式布局优化
- ✅ 将所有响应式断点从 `1024px` 调整为 `1000px`
- ✅ 优化平板设备（特别是1024px宽度设备）的显示效果

## 🚀 部署步骤

### 步骤1：本地构建前端
```bash
# 进入前端目录
cd frontend

# 安装依赖（如果需要）
npm install

# 构建生产版本
npm run build
```

### 步骤2：上传到云服务器
```bash
# 方式1：使用scp上传（推荐）
scp -r dist/* root@你的服务器IP:/opt/quwei/frontend/dist/

# 方式2：如果有压缩需求
tar -czf frontend-dist.tar.gz -C dist .
scp frontend-dist.tar.gz root@你的服务器IP:/opt/quwei/
```

### 步骤3：登录云服务器
```bash
ssh root@你的服务器IP
```

### 步骤4：解压文件（如果使用方式2）
```bash
cd /opt/quwei
tar -xzf frontend-dist.tar.gz -C frontend/dist/
rm frontend-dist.tar.gz
```

### 步骤5：重启前端容器
```bash
cd /opt/quwei
docker-compose restart frontend
```

### 步骤6：验证部署
```bash
# 检查容器状态
docker-compose ps

# 检查前端容器日志
docker-compose logs frontend

# 测试API连接
curl http://localhost:8080/api/tasks
```

## 🔍 功能验证清单

### 敖丙小课堂验证
- [ ] 访问孩子端，进入敖丙小课堂
- [ ] 选择答案并提交
- [ ] 确认提交后显示"答案已提交"遮罩
- [ ] 刷新页面，确认遮罩依然显示（基于真实任务状态）
- [ ] 在家长端确认额外任务状态为"进行中"
- [ ] 家长端点击"完成任务"按钮，确认可以将任务标记为已完成

### 响应式布局验证
- [ ] 在1025px宽度下查看页面（应显示桌面样式）
- [ ] 在1000px宽度下查看页面（应显示移动端样式）
- [ ] 在平板设备（iPad等）上测试显示效果
- [ ] 确认所有页面的响应式效果正常

## 🛠️ 故障排除

### 如果前端无法访问
```bash
# 检查nginx配置
docker-compose exec nginx nginx -t

# 重启nginx
docker-compose restart nginx

# 检查前端文件是否正确上传
ls -la /opt/quwei/frontend/dist/
```

### 如果API请求失败
```bash
# 检查后端容器状态
docker-compose logs backend

# 重启后端容器
docker-compose restart backend

# 检查数据库连接
docker-compose logs mysql
```

### 如果样式显示异常
```bash
# 清除浏览器缓存
# 或者强制刷新页面 (Ctrl+F5)

# 检查静态文件是否正确部署
curl -I http://你的域名/assets/index.css
```

## 📱 测试设备建议

### 桌面端测试
- Chrome/Firefox/Safari 浏览器
- 窗口宽度 > 1000px

### 平板端测试
- iPad (1024×768)
- iPad Pro (1024×1366)
- Android 平板

### 移动端测试
- iPhone (375×667)
- Android 手机 (360×640)

## 🔄 回滚方案

如果部署后发现问题，可以快速回滚：

```bash
# 1. 备份当前版本
cd /opt/quwei
cp -r frontend/dist frontend/dist.backup.$(date +%Y%m%d_%H%M%S)

# 2. 恢复之前的版本（如果有备份）
# cp -r frontend/dist.backup.YYYYMMDD_HHMMSS/* frontend/dist/

# 3. 重启容器
docker-compose restart frontend
```

## 📞 联系信息

如果部署过程中遇到问题，请检查：
1. 服务器磁盘空间是否充足
2. Docker 服务是否正常运行
3. 网络连接是否稳定
4. 防火墙设置是否正确

---

**部署完成后，记得测试所有核心功能确保正常运行！**
