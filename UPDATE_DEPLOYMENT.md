# 云服务更新部署指南

## 📋 本次更新内容

### 1. 敖丙小课堂功能优化
- ✅ 修改答案提交后任务状态从"已完成"改为"进行中"
- ✅ 遮罩层显示条件改为基于真实任务状态（防止刷新后重复编辑）
- ✅ 遮罩层文字显示为"答案已提交"

### 2. 响应式布局优化
- ✅ 将所有响应式断点从 `1024px` 调整为 `1000px`
- ✅ 优化平板设备（特别是1024px宽度设备）的显示效果

### 3. 后端功能增强
- ✅ 任务状态更新时自动设置时间戳（begin_at, completed_at）
- ✅ 优化任务状态转换逻辑

## 🚀 **快速部署命令**

```bash
# 本地构建前端和后端
cd frontend && npm run build
cd ../backend && mvn clean package -DskipTests

# 上传到服务器
scp -r frontend/dist/* root@你的服务器IP:/opt/quwei/frontend/dist/
scp backend/target/*.jar root@你的服务器IP:/opt/quwei/backend/

# 服务器重新创建容器（注意顺序）
ssh root@你的服务器IP
cd /opt/quwei
docker-compose down
docker-compose up -d --force-recreate
```

## 🚀 详细部署步骤

### 步骤1：本地构建前端
```bash
# 进入前端目录
cd frontend

# 安装依赖（如果需要）
npm install

# 构建生产版本
npm run build
```

### 步骤2：本地构建后端
```bash
# 进入后端目录
cd backend

# 清理并编译（如果使用Maven）
mvn clean package -DskipTests

# 或者如果使用Gradle
# ./gradlew clean build -x test
```

### 步骤3：上传文件到云服务器
```bash
# 上传前端文件
scp -r frontend/dist/* root@你的服务器IP:/opt/quwei/frontend/dist/

# 上传后端JAR文件（Maven构建）
scp backend/target/*.jar root@你的服务器IP:/opt/quwei/backend/

# 或者压缩上传（推荐大文件）
tar -czf frontend-dist.tar.gz -C frontend/dist .
tar -czf backend-jar.tar.gz -C backend/target *.jar
scp frontend-dist.tar.gz backend-jar.tar.gz root@你的服务器IP:/opt/quwei/
```

### 步骤4：登录云服务器
```bash
ssh root@你的服务器IP
```

### 步骤5：解压文件（如果使用压缩上传）
```bash
cd /opt/quwei

# 解压前端文件
tar -xzf frontend-dist.tar.gz -C frontend/dist/

# 解压后端文件
tar -xzf backend-jar.tar.gz -C backend/

# 清理压缩文件
rm frontend-dist.tar.gz backend-jar.tar.gz
```

### 步骤6：重新创建并启动容器
```bash
cd /opt/quwei

# 停止所有服务
docker-compose down

# 重新创建并启动服务（后端先启动）
docker-compose up -d mysql
sleep 10

docker-compose up -d backend
sleep 30

docker-compose up -d frontend nginx

# 或者一次性重新创建所有服务
# docker-compose up -d --force-recreate
```

### 步骤7：验证部署
```bash
# 检查所有容器状态
docker-compose ps

# 检查容器启动日志
docker-compose logs mysql
docker-compose logs backend
docker-compose logs frontend
docker-compose logs nginx

# 测试后端API连接
curl http://localhost:8080/api/tasks

# 测试任务状态更新API
curl -X PUT http://localhost:8080/api/tasks/1 \
  -H "Content-Type: application/json" \
  -d '{"status":"进行中"}'

# 检查容器资源使用情况
docker stats --no-stream
```

## 🔍 功能验证清单

### 后端API验证
- [ ] 后端容器正常启动，无错误日志
- [ ] 数据库连接正常
- [ ] GET /api/tasks/today-tasks?priorities=extra 返回额外任务数据
- [ ] PUT /api/tasks/{id} 可以正常更新任务状态
- [ ] 任务状态从"未完成"→"进行中"时自动设置begin_at时间
- [ ] 任务状态从"进行中"→"已完成"时自动设置completed_at时间

### 敖丙小课堂验证
- [ ] 访问孩子端，进入敖丙小课堂
- [ ] 选择答案并提交
- [ ] 确认提交后显示"答案已提交"遮罩
- [ ] 刷新页面，确认遮罩依然显示（基于真实任务状态）
- [ ] 在家长端确认额外任务状态为"进行中"
- [ ] 家长端点击"完成任务"按钮，确认可以将任务标记为已完成

### 响应式布局验证
- [ ] 在1025px宽度下查看页面（应显示桌面样式）
- [ ] 在1000px宽度下查看页面（应显示移动端样式）
- [ ] 在平板设备（iPad等）上测试显示效果
- [ ] 确认所有页面的响应式效果正常

## � **容器管理最佳实践**

### 为什么要重新创建容器而不是重启？

1. **代码更新**：新的JAR文件需要重新加载到容器中
2. **配置变更**：确保所有配置文件重新读取
3. **内存清理**：清除可能的内存泄漏和缓存问题
4. **依赖更新**：确保所有依赖库正确加载

### 推荐的容器重建流程

```bash
# 方式1：完全重建（推荐）
docker-compose down
docker-compose up -d --force-recreate

# 方式2：分步重建（更安全）
docker-compose down
docker-compose up -d mysql        # 先启动数据库
sleep 10
docker-compose up -d backend      # 再启动后端
sleep 30
docker-compose up -d frontend nginx  # 最后启动前端和代理

# 方式3：单独重建某个服务
docker-compose stop backend
docker-compose rm -f backend
docker-compose up -d backend
```

### 清理无用资源

```bash
# 清理停止的容器
docker container prune -f

# 清理无用的镜像
docker image prune -f

# 清理无用的网络
docker network prune -f

# 查看磁盘使用情况
docker system df
```

## �🛠️ 故障排除

### 如果后端启动失败
```bash
# 检查后端容器日志
docker-compose logs backend

# 检查JAR文件是否存在
ls -la /opt/quwei/backend/*.jar

# 检查数据库连接
docker-compose logs mysql

# 重新创建后端容器
docker-compose stop backend
docker-compose rm -f backend
docker-compose up -d backend
```

### 如果前端无法访问
```bash
# 检查nginx配置
docker-compose exec nginx nginx -t

# 重新创建nginx和前端容器
docker-compose stop frontend nginx
docker-compose rm -f frontend nginx
docker-compose up -d frontend nginx

# 检查前端文件是否正确上传
ls -la /opt/quwei/frontend/dist/
```

### 如果API请求失败
```bash
# 检查后端容器状态
docker-compose ps backend

# 检查后端日志中的错误信息
docker-compose logs backend | tail -50

# 测试数据库连接
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"

# 重新创建整个服务栈
docker-compose down
docker-compose up -d --force-recreate
```

### 如果样式显示异常
```bash
# 清除浏览器缓存
# 或者强制刷新页面 (Ctrl+F5)

# 检查静态文件是否正确部署
curl -I http://你的域名/assets/index.css
```

## 📱 测试设备建议

### 桌面端测试
- Chrome/Firefox/Safari 浏览器
- 窗口宽度 > 1000px

### 平板端测试
- iPad (1024×768)
- iPad Pro (1024×1366)
- Android 平板

### 移动端测试
- iPhone (375×667)
- Android 手机 (360×640)

## 🔄 回滚方案

如果部署后发现问题，可以快速回滚：

```bash
# 1. 备份当前版本
cd /opt/quwei
cp -r frontend/dist frontend/dist.backup.$(date +%Y%m%d_%H%M%S)
cp backend/*.jar backend/backup.$(date +%Y%m%d_%H%M%S).jar

# 2. 恢复之前的版本（如果有备份）
# 恢复前端
# cp -r frontend/dist.backup.YYYYMMDD_HHMMSS/* frontend/dist/

# 恢复后端
# cp backend/backup.YYYYMMDD_HHMMSS.jar backend/quwei-backend.jar

# 3. 重新创建容器
docker-compose down
docker-compose up -d --force-recreate
```

## 📞 联系信息

如果部署过程中遇到问题，请检查：
1. 服务器磁盘空间是否充足
2. Docker 服务是否正常运行
3. 网络连接是否稳定
4. 防火墙设置是否正确

---

**部署完成后，记得测试所有核心功能确保正常运行！**
