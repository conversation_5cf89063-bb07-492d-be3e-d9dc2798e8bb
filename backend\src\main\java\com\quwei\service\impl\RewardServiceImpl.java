package com.quwei.service.impl;

import com.quwei.dto.RewardDTO;
import com.quwei.dto.RewardResponseDTO;
import com.quwei.entity.Reward;
import com.quwei.repository.RewardRepository;
import com.quwei.service.RewardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RewardServiceImpl implements RewardService {
    private static final Logger logger = LoggerFactory.getLogger(RewardServiceImpl.class);
    private final RewardRepository rewardRepository;

    @Autowired
    public RewardServiceImpl(RewardRepository rewardRepository) {
        this.rewardRepository = rewardRepository;
    }

    @Override
    public Reward getRewardById(Integer id) {
        return rewardRepository.findById(id).orElse(null);
    }

    @Override
    public RewardResponseDTO getRewards(int page, int pageSize, String sort) {
        Sort sortObj = createSortFromParam(sort);
        Pageable pageable = PageRequest.of(page, pageSize, sortObj);
        Page<Reward> rewardPage = rewardRepository.findAll(pageable);

        List<RewardDTO> rewardDTOs = rewardPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        RewardResponseDTO responseDTO = new RewardResponseDTO();
        responseDTO.setRewards(rewardDTOs);
        responseDTO.setPagination(new RewardResponseDTO.Pagination(
                rewardPage.getTotalElements(),
                page,
                pageSize,
                rewardPage.getTotalPages()
        ));

        return responseDTO;
    }

    @Override
    public boolean updateReward(Integer id, String name, String description, Integer fragmentCount, Integer stock) {
        return rewardRepository.findById(id).map(reward -> {
            if (name != null) reward.setName(name);
            if (description != null) reward.setDescription(description);
            if (fragmentCount != null) reward.setFragmentCount(fragmentCount);
            if (stock != null) reward.setStock(stock);
            rewardRepository.save(reward);
            return true;
        }).orElse(false);
    }

    @Override
    public boolean deleteReward(Integer id) {
        if (rewardRepository.existsById(id)) {
            rewardRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private Sort createSortFromParam(String sort) {
        if (sort == null || sort.isEmpty()) {
            return Sort.by(Sort.Direction.ASC, "id");
        }

        String[] sortParams = sort.split(",");
        String property = sortParams[0];
        Sort.Direction direction = sortParams.length > 1 && "desc".equalsIgnoreCase(sortParams[1]) ?
                Sort.Direction.DESC : Sort.Direction.ASC;

        return Sort.by(direction, property);
    }

    private RewardDTO convertToDTO(Reward reward) {
        RewardDTO dto = new RewardDTO();
        dto.setId(reward.getId());
        dto.setName(reward.getName());
        dto.setDescription(reward.getDescription());
        dto.setFragment_count(reward.getFragmentCount());
        dto.setStock(reward.getStock());
        dto.setCreated_at(reward.getCreatedAt());
        return dto;
    }

    @Override
    public boolean createReward(String name, String description, Integer fragmentCount, Integer stock) {
        logger.info("Creating new reward: name={}, description={}, fragmentCount={}, stock={}", 
                   name, description, fragmentCount, stock);
                   
        if (name == null || name.trim().isEmpty()) {
            logger.error("Failed to create reward: name is required");
            throw new IllegalArgumentException("奖励名称不能为空");
        }
        
        if (fragmentCount == null || fragmentCount <= 0) {
            logger.error("Failed to create reward: invalid fragmentCount: {}", fragmentCount);
            throw new IllegalArgumentException("所需碎片数量必须大于0");
        }
        
        if (stock == null || stock < 0) {
            logger.error("Failed to create reward: invalid stock: {}", stock);
            throw new IllegalArgumentException("库存数量不能为负数");
        }
        
        try {
            Reward reward = new Reward();
            reward.setName(name);
            reward.setDescription(description);
            reward.setFragmentCount(fragmentCount);
            reward.setStock(stock);
            reward.setCreatedAt(LocalDateTime.now());
            
            reward = rewardRepository.save(reward);
            logger.info("Successfully created reward with id: {}", reward.getId());
            return true;
        } catch (Exception e) {
            logger.error("Failed to create reward", e);
            throw new RuntimeException("创建奖励失败: " + e.getMessage(), e);
        }
    }
}