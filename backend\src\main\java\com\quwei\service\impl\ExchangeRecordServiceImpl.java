package com.quwei.service.impl;

import com.quwei.dto.ExchangeRecordDTO;
import com.quwei.dto.ExchangeRecordResponseDTO;
import com.quwei.entity.ExchangeRecord;
import com.quwei.entity.Reward;
import com.quwei.repository.ExchangeRecordRepository;
import com.quwei.repository.RewardRepository;
import com.quwei.service.ExchangeRecordService;
import com.quwei.service.RewardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExchangeRecordServiceImpl implements ExchangeRecordService {

    private final ExchangeRecordRepository exchangeRecordRepository;
    private final RewardService rewardService;
    private final RewardRepository rewardRepository;

    @Autowired
    public ExchangeRecordServiceImpl(ExchangeRecordRepository exchangeRecordRepository,RewardService rewardService, RewardRepository rewardRepository) {
        this.exchangeRecordRepository = exchangeRecordRepository;
        this.rewardService = rewardService;
        this.rewardRepository = rewardRepository;
    }

    @Override
    public ExchangeRecordResponseDTO getExchangeRecordsByUserId(int page, int pageSize) {
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        Page<ExchangeRecord> exchangeRecordPage = exchangeRecordRepository.findAll(pageable);

        List<ExchangeRecordDTO> exchangeRecordDTOs = exchangeRecordPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        ExchangeRecordResponseDTO responseDTO = new ExchangeRecordResponseDTO();
        responseDTO.setExchangeRecords(exchangeRecordDTOs);
        responseDTO.setPagination(new ExchangeRecordResponseDTO.Pagination(
                exchangeRecordPage.getTotalElements(),
                page,
                pageSize,
                exchangeRecordPage.getTotalPages()
        ));

        return responseDTO;
    }

    private ExchangeRecordDTO convertToDTO(ExchangeRecord exchangeRecord) {
        ExchangeRecordDTO dto = new ExchangeRecordDTO();
        dto.setId(exchangeRecord.getId());
        dto.setReward_id(exchangeRecord.getRewardId());
        dto.setExchange_time(exchangeRecord.getExchangeTime());
        dto.setFragmentsUsed(exchangeRecord.getFragmentsUsed());
        // 获取奖品信息
        Reward reward = rewardService.getRewardById(exchangeRecord.getRewardId());
        if (reward != null) {
            dto.setRewardName(reward.getName());
        }
        return dto;
    }

    @Transactional
    public ExchangeRecordDTO createNewExchangeRecord(ExchangeRecordDTO request) {
        // Validate reward exists
        Reward reward = rewardRepository.findById(request.getReward_id())
            .orElseThrow(() -> new IllegalArgumentException("Reward not found with id: " + request.getReward_id()));

        // Create and save exchange record
        ExchangeRecord exchangeRecord = new ExchangeRecord();
        exchangeRecord.setRewardId(request.getReward_id());
        exchangeRecord.setFragmentsUsed(request.getFragmentsUsed());
        exchangeRecord.setExchangeTime(LocalDateTime.now());
        exchangeRecord.setExchangeDate(LocalDateTime.now());
        exchangeRecord = exchangeRecordRepository.save(exchangeRecord);

        // Convert to DTO
        ExchangeRecordDTO dto = new ExchangeRecordDTO();
        dto.setId(exchangeRecord.getId());
        dto.setReward_id(exchangeRecord.getRewardId());
        dto.setExchange_time(exchangeRecord.getExchangeTime());
        dto.setFragmentsUsed(exchangeRecord.getFragmentsUsed());
        dto.setRewardName(reward.getName());

        return dto;
    }
}