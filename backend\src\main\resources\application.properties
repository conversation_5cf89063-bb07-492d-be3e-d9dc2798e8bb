spring.application.name=quwei

# 默认配置（开发环境）
server.port=8080

# 数据库连接配置（开发环境，可选）
spring.datasource.url=**********************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# 允许在没有数据库的情况下启动（用于构建）
# spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# 日志配置
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
