package com.quwei.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "tasks")
public class Task {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false)
    private String title;

    private String description;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TaskStatus status;

    @Enumerated(EnumType.STRING)
    private TaskPriority priority;

    private LocalDateTime created_at;

    private LocalDateTime begin_at;

    private LocalDateTime completed_at;

    private Integer experience_points;

    private Integer fragment_reward;

    public enum TaskStatus {
        未完成, 进行中, 已完成
    }

    public enum TaskPriority {
        重点任务, 基础任务, 额外任务
    }

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public TaskStatus getStatus() { return status; }
    public void setStatus(TaskStatus status) { this.status = status; }
    public TaskPriority getPriority() { return priority; }
    public void setPriority(TaskPriority priority) { this.priority = priority; }
    public LocalDateTime getCreated_at() { return created_at; }
    public void setCreated_at(LocalDateTime created_at) { this.created_at = created_at; }
    public LocalDateTime getBegin_at() { return begin_at; }
    public void setBegin_at(LocalDateTime begin_at) { this.begin_at = begin_at; }
    public LocalDateTime getCompleted_at() { return completed_at; }
    public void setCompleted_at(LocalDateTime completed_at) { this.completed_at = completed_at; }
    public Integer getExperience_points() { return experience_points; }
    public void setExperience_points(Integer experience_points) { this.experience_points = experience_points; }
    public Integer getFragment_reward() { return fragment_reward; }
    public void setFragment_reward(Integer fragment_reward) { this.fragment_reward = fragment_reward; }
}